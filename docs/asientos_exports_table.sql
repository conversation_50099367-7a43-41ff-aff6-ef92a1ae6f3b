DROP SEQUENCE  "LAPAMPA"."ASIENTOS_EXPORTS_ID_SEQ";

CREATE SEQUENCE  "LAPAMPA"."ASIENTOS_EXPORTS_ID_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

 DROP TABLE  "LAPAMPA"."ASIENTOS_EXPORTS";
 
  CREATE TABLE "LAPAMPA"."ASIENTOS_EXPORTS" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."ASIENTOS_EXPORTS_ID_SEQ"."NEXTVAL", 
	"CAMPOS" CLOB, 
	"CREADO" DATE, 
	"PROCESO_ID" NUMBER(19,0), 
	"PROCESO_REGISTROS" NUMBER(19,0) DEFAULT 0, 
	"PROCESO_INICIO" DATE, 
	"PROCESO_FINAL" DATE, 
	"RESULTADO" CLOB, 
	"PROCESO_ERRORES" NUMBER DEFAULT 0, 
	"PROCEDIMIENTO" VARCHAR2(250 BYTE), 
	"NOMBRE" VARCHAR2(100 BYTE), 
	"ESTADO" VARCHAR2(100 BYTE), 
	"CABECERAS" CLOB, 
	"URL" VARCHAR2(2048 BYTE), 
	"REQUEST" VARCHAR2(2048 BYTE), 
	"USER_LOG" VARCHAR2(2048 BYTE), 
	"EXPORTADO" DATE, 
	"ANIO" NUMBER(4,0), 
	"MES" NUMBER(2,0), 
	"TIPO_PROCESO" VARCHAR2(200 BYTE), 
	"EXPORTABLE" VARCHAR2(1 BYTE) DEFAULT 'N'
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" 
 LOB ("CAMPOS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW CHUNK 8192
  CACHE  NOCOMPRESS  KEEP_DUPLICATES 
  STORAGE(INITIAL 106496 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) 
 LOB ("RESULTADO") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES 
  STORAGE(INITIAL 106496 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) 
 LOB ("CABECERAS") STORE AS SECUREFILE (
  TABLESPACE "USERS" ENABLE STORAGE IN ROW CHUNK 8192
  CACHE  NOCOMPRESS  KEEP_DUPLICATES 
  STORAGE(INITIAL 106496 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) ;

   COMMENT ON COLUMN "LAPAMPA"."ASIENTOS_EXPORTS"."PROCESO_ID" IS 'Liq proceso id';
   COMMENT ON COLUMN "LAPAMPA"."ASIENTOS_EXPORTS"."PROCEDIMIENTO" IS 'Url procedimiento exportacion';
   COMMENT ON COLUMN "LAPAMPA"."ASIENTOS_EXPORTS"."URL" IS 'Url procedimiento generacion';

--------------------------------------------------------
--  DDL for Index ASIENTOS_EXPORTS_PK
--------------------------------------------------------

  CREATE UNIQUE INDEX "LAPAMPA"."ASIENTOS_EXPORTS_PK" ON "LAPAMPA"."ASIENTOS_EXPORTS" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index UNIQUEPROCEDIMIENTOCONSTRAINT
--------------------------------------------------------

  CREATE UNIQUE INDEX "LAPAMPA"."UNIQUEPROCEDIMIENTOCONSTRAINT" ON "LAPAMPA"."ASIENTOS_EXPORTS" ("ID", "PROCESO_ID", "PROCEDIMIENTO") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  Constraints for Table ASIENTOS_EXPORTS
--------------------------------------------------------

  ALTER TABLE "LAPAMPA"."ASIENTOS_EXPORTS" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "LAPAMPA"."ASIENTOS_EXPORTS" ADD CONSTRAINT "V_JSON_CAMPOS" CHECK (CAMPOS IS JSON) ENABLE;
  ALTER TABLE "LAPAMPA"."ASIENTOS_EXPORTS" ADD CONSTRAINT "V_JSON_CABECERAS" CHECK (CABECERAS IS JSON) ENABLE;
  ALTER TABLE "LAPAMPA"."ASIENTOS_EXPORTS" ADD CONSTRAINT "ASIENTOS_EXPORTS_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE;
  ALTER TABLE "LAPAMPA"."ASIENTOS_EXPORTS" ADD CONSTRAINT "UNIQUEPROCEDIMIENTOCONSTRAINT" UNIQUE ("ID", "PROCESO_ID", "PROCEDIMIENTO")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE;
  ALTER TABLE "LAPAMPA"."ASIENTOS_EXPORTS" MODIFY ("EXPORTABLE" NOT NULL ENABLE);
   