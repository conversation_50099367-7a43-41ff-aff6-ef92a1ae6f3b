DROP SEQUENCE  "LAPAMPA"."SUE_PENSSUC_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_PENSSUC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_PENSSUC" 
CREATE TABLE "LAPAMPA"."SUE_PENSSUC" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_PENSSUC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"ANIOL" NUMBER(10,0), 
	"MESL" NUMBER(10,0), 
	"NMBR" VARCHAR2(10 BYTE), 
	"PRCUIL" NUMBER(15,0), 
	"SUCU" NUMBER(10,0), 
	"TLIQ" VARCHAR2(2 BYTE), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	 CONSTRAINT "SUE_PENSSUC_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 0 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."SUE_PENSSUC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_PENSSUC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_PENSSUC"."ESTADONSL" IS 'No se exporta';