DROP SEQUENCE  "LAPAMPA"."SUE_TFDETALLE_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_TFDETALLE_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_TFDETALLE"
CREATE TABLE "LAPAMPA"."SUE_TFDETALLE" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_TFDETALLE_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	"ANIOLIQ" NUMBER(10,0), 
	"BCOPAGO" NUMBER(10,0), 
	"CARATU" VARCHAR2(120 BYTE), 
	"CBU1" NUMBER(30,0), 
	"CBU2" VARCHAR2(34 BYTE), 
	"CIRC" NUMBER(10,0), 
	"CLIENTE" NUMBER(15,0), 
	"CODDOC" NUMBER(10,0), 
	"CODENTE" NUMBER(10,0), 
	"CODERROR" NUMBER(10,0), 
	"CODORI" NUMBER(10,0), 
	"CODPROV" VARCHAR2(22 BYTE), 
	"CONVE" NUMBER(10,0), 
	"CONVENIO" NUMBER(10,0), 
	"CPTO" NUMBER(10,0), 
	"CTADES" NUMBER(15,0), 
	"CTAORI" NUMBER(15,0), 
	"CUENTA" NUMBER(10,0), 
	"CUITPROV" NUMBER(15,0), 
	"ENTRADA" NUMBER(10,0), 
	"ENVIO" NUMBER(10,0), 
	"ESTADO" VARCHAR2(2 BYTE), 
	"EXPTE" VARCHAR2(22 BYTE), 
	"FECHAENV" DATE, 
	"FECHAORI" DATE, 
	"FECHAPAG" DATE, 
	"FORMAPAG" NUMBER(10,0), 
	"HAB" VARCHAR2(2 BYTE), 
	"IMPORTE" NUMBER(17,2), 
	"JUR" VARCHAR2(2 BYTE), 
	"JUZGA" VARCHAR2(60 BYTE), 
	"MARCALIS" VARCHAR2(2 BYTE), 
	"MESLIQ" NUMBER(10,0), 
	"MONEDA" NUMBER(10,0), 
	"NOMPROV" VARCHAR2(120 BYTE), 
	"NOMSUC" VARCHAR2(40 BYTE), 
	"NORDEN" VARCHAR2(24 BYTE), 
	"NRODOC" NUMBER(15,0), 
	"NROLIQ" VARCHAR2(22 BYTE), 
	"OBSER" VARCHAR2(200 BYTE), 
	"OFICIO" VARCHAR2(20 BYTE), 
	"PERIODO" NUMBER(10,0), 
	"SECRE" VARCHAR2(6 BYTE), 
	"SISDES" NUMBER(10,0), 
	"SISORI" NUMBER(10,0), 
	"SUCDES" NUMBER(10,0), 
	"SUCORI" NUMBER(10,0), 
	"SUCPAGO" NUMBER(10,0), 
	"SUCURSAL" NUMBER(10,0), 
	"TIPODES" NUMBER(10,0), 
	"TIPOMOV" NUMBER(10,0), 
	"TIPORI" NUMBER(10,0), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	 CONSTRAINT "SUE_TFDETALLE_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 0 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."SUE_TFDETALLE"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_TFDETALLE"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_TFDETALLE"."ESTADONSL" IS 'No se exporta';

