DROP SEQUENCE  "LAPAMPA"."SUE_MOV<PERSON><PERSON>AN_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_MOV<PERSON>SBAN_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_<PERSON>OVGASBAN"
CREATE TABLE "LAPAMPA"."SUE_MOVGASBAN" 
   ("ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_MOVGASBAN_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"ANIOLIQ" NUMBER(10,0), 
	"CARATULA" VARCHAR2(100 BYTE), 
	"COMPN" NUMBER(10,0), 
	"ESTADO" VARCHAR2(2 BYTE), 
	"FECHALIQ" DATE, 
	"<PERSON>EC<PERSON><PERSON><PERSON>" DATE, 
	"FINFUN" NUMBER(10,0), 
	"HORA" NUMBER(10,0), 
	"IDLIQ" VARCHAR2(10 BYTE), 
	"IMPASIG" NUMBER(17,2), 
	"IMPLIQ" NUMBER(17,2), 
	"MESLIQ" NUMBER(10,0), 
	"NROLIQ" VARCHAR2(22 BYTE), 
	"NROPAR" NUMBER(10,0), 
	"OPER" VARCHAR2(8 BYTE), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	 CONSTRAINT "SUEMOVGASBAN_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."SUE_MOVGASBAN"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_MOVGASBAN"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_MOVGASBAN"."ESTADONSL" IS 'No se exporta';