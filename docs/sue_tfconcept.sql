DROP SEQUENCE  "LAPAMPA"."SUE_TFCONCEPT_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_TFCONCEPT_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_TFCONCEPT"
CREATE TABLE "LAPAMPA"."SUE_TFCONCEPT" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_TFCONCEPT_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	"ANIOCPTO" NUMBER(10,0), 
	"CPTO" NUMBER(10,0), 
	"DESCRIP" VARCHAR2(120 BYTE), 
	"MESCPTO" NUMBER(10,0), 
	"TIPOCPTO" VARCHAR2(6 BYTE), 
	"MARCACOPIADO" VARCHAR2(2 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 0 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
  CREATE UNIQUE INDEX "LAPAMPA"."SUE_TFCONCEPT_PK" ON "LAPAMPA"."SUE_TFCONCEPT" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
ALTER TABLE "LAPAMPA"."SUE_TFCONCEPT" ADD CONSTRAINT "SUE_TFCONCEPT_PK" PRIMARY KEY ("ID")
  USING INDEX "LAPAMPA"."SUE_TFCONCEPT_PK"  ENABLE;

   COMMENT ON COLUMN "LAPAMPA"."SUE_TFCONCEPT"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_TFCONCEPT"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_TFCONCEPT"."ESTADONSL" IS 'No se exporta';

  CREATE INDEX "LAPAMPA"."SUE_TFCONCEPT_IDX" ON "LAPAMPA"."SUE_TFCONCEPT" ("ANIOCPTO", "MESCPTO", "CPTO") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

