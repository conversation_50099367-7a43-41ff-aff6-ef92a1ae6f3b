DROP SEQUENCE  "LAPAMPA"."SUE_ORDPAGC_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_ORDPAGC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_ORDPAGC"
CREATE TABLE "LAPAMPA"."SUE_ORDPAGC" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_ORDPAGC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"CODBAN" NUMBER(10,0), 
	"CODHAB" VARCHAR2(2 BYTE), 
	"CODIMP" VARCHAR2(2 BYTE), 
	"CPAG" NUMBER(10,0), 
	"CTALECOP" NUMBER(15,0), 
	"CTAPESOS" NUMBER(15,0), 
	"CTASIS" NUMBER(10,0), 
	"CTASISL" NUMBER(10,0), 
	"CTASUC" NUMBER(10,0), 
	"CTASUCL" NUMBER(10,0), 
	"CTATIPO" NUMBER(10,0), 
	"CTATIPOL" NUMBER(10,0), 
	"CUENTA" NUMBER(10,0), 
	"ESTPED" VARCHAR2(2 BYTE), 
	"FECHAENV" DATE, 
	"FECHAPED" DATE, 
	"LIQANIO" NUMBER(10,0), 
	"LIQMES" NUMBER(10,0), 
	"LIQTIPO" VARCHAR2(2 BYTE), 
	"MARCA" VARCHAR2(2 BYTE), 
	"NROPED" NUMBER(10,0), 
	"TOTBRUTO" NUMBER(17,2), 
	"TOTCRED" NUMBER(17,2), 
	"TOTCREDL" NUMBER(17,2), 
	"TOTNETO" NUMBER(17,2), 
	"TOTNETOL" NUMBER(17,2), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	 CONSTRAINT "SUE_ORDPAGC_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 0 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."SUE_ORDPAGC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_ORDPAGC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_ORDPAGC"."ESTADONSL" IS 'No se exporta';