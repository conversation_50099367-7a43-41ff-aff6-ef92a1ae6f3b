DROP SEQUENCE  "LAPAMPA"."SUE_LIQUISUS_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_LIQUISUS_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_LIQUISUS" 
CREATE TABLE "LAPAMPA"."SUE_LIQUISUS" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_LIQUISUS_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"ANIO" NUMBER(10,0), 
	"CAR" VARCHAR2(2 BYTE), 
	"CODOP" VARCHAR2(2 BYTE), 
	"CPAG" NUMBER(10,0), 
	"CTABAN" NUMBER(10,0), 
	"CUENTA" NUMBER(10,0), 
	"DIVEXTRA" NUMBER(10,0), 
	"EXPEDA" NUMBER(10,0), 
	"EXPEDI" VARCHAR2(2 BYTE), 
	"EXPEDN" VARCHAR2(12 BYTE), 
	"FECCONF" DATE, 
	"FECHA" DATE, 
	"FECORI" DATE, 
	"HORA" NUMBER(10,0), 
	"IDLIQ" VARCHAR2(10 BYTE), 
	"IMPORTE" NUMBER(17,2), 
	"JUR" VARCHAR2(2 BYTE), 
	"LIQANIO" NUMBER(10,0), 
	"LIQNRO" VARCHAR2(12 BYTE), 
	"LIQSUB" NUMBER(10,0), 
	"LIQUINT" VARCHAR2(2 BYTE), 
	"NEGANIO" NUMBER(10,0), 
	"NEGINT" VARCHAR2(2 BYTE), 
	"NEGNRO" VARCHAR2(12 BYTE), 
	"NROPAR" NUMBER(10,0), 
	"NROPROV" NUMBER(10,0), 
	"OPER" VARCHAR2(8 BYTE), 
	"PPRI" NUMBER(10,0), 
	"RENGLON" NUMBER(10,0), 
	"TIPMOV" VARCHAR2(2 BYTE), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	 CONSTRAINT "SUE_LIQUISUS_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."SUE_LIQUISUS"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_LIQUISUS"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_LIQUISUS"."ESTADONSL" IS 'No se exporta';