DROP SEQUENCE  "LAPAMPA"."SUE_TFDESCUEN_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_TFDESCUEN_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_TFDESCUEN"
CREATE TABLE "LAPAMPA"."SUE_TFDESCUEN" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_TFDESCUEN_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	"ANIOLIQ" NUMBER(10,0), 
	"CODENTE" NUMBER(10,0), 
	"CODORI" NUMBER(10,0), 
	"CONVE" NUMBER(10,0), 
	"CONVENIO" NUMBER(10,0), 
	"CPTO" NUMBER(10,0), 
	"CUENTA" NUMBER(10,0), 
	"ENTRADA" NUMBER(10,0), 
	"ENVIO" NUMBER(10,0), 
	"FECHAORI" DATE, 
	"HAB" VARCHAR2(2 BYTE), 
	"IMPORTE" NUMBER(17,2), 
	"JUR" VARCHAR2(2 BYTE), 
	"MESLIQ" NUMBER(10,0), 
	"MONEDA" NUMBER(10,0), 
	"NROLIQ" VARCHAR2(22 BYTE), 
	"TIPOMOV" NUMBER(10,0), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	 CONSTRAINT "SUE_TFDESCUEN_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 0 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."SUE_TFDESCUEN"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_TFDESCUEN"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_TFDESCUEN"."ESTADONSL" IS 'No se exporta';

