DROP SEQUENCE  "LAPAMPA"."TEMP_SUELIQUISUS_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUELIQUISUS_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUELIQUISUS";

CREATE TABLE "LAPAMPA"."TEMP_SUELIQUISUS" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUELIQUISUS_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIO NUMBER(10,0), 
	CAR VARCHAR2(2 BYTE), 
	CODOP VARCHAR2(2 BYTE), 
	CPAG NUMBER(10,0), 
	CT<PERSON>AN NUMBER(10,0), 
	CUENTA NUMBER(10,0), 
	DIVEXTRA NUMBER(10,0), 
	EXPEDA NUMBER(10,0), 
	EXPEDI VARCHAR2(2 BYTE), 
	EXPEDN VARCHAR2(12 BYTE), 
	FECCONF DATE, 
	FECHA DATE, 
	FECORI DATE, 
	HORA NUMBER(10,0), 
	IDLIQ VARCHAR2(10 BYTE), 
	IMPORTE NUMBER(17,2), 
	JUR VARCHAR2(2 BYTE), 
	LIQANIO NUMBER(10,0), 
	LIQNRO VARCHAR2(12 BYTE), 
	LIQSUB NUMBER(10,0), 
	LIQUINT VARCHAR2(2 BYTE), 
	NEGANIO NUMBER(10,0), 
	NEGINT VARCHAR2(2 BYTE), 
	NEGNRO VARCHAR2(12 BYTE), 
	NROPAR NUMBER(10,0), 
	NROPROV NUMBER(10,0), 
	OPER VARCHAR2(8 BYTE), 
	PPRI NUMBER(10,0), 
	RENGLON NUMBER(10,0), 
	TIPMOV VARCHAR2(2 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE), 
	 CONSTRAINT "SLQS_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUELIQUISUS"."ID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUELIQUISUS"."ASIENTOEXPORTID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUELIQUISUS"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUEMOVGASBAN_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEMOVGASBAN_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEMOVGASBAN";

CREATE TABLE "LAPAMPA"."TEMP_SUEMOVGASBAN" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEMOVGASBAN_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE), 
	ANIOLIQ NUMBER(10,0), 
	CARATULA VARCHAR2(100 BYTE), 
	COMPN NUMBER(10,0), 
	ESTADO VARCHAR2(2 BYTE), 
	FECHALIQ DATE, 
	FECHAPRO DATE, 
	FINFUN NUMBER(10,0), 
	HORA NUMBER(10,0), 
	IDLIQ VARCHAR2(10 BYTE), 
	IMPASIG NUMBER(17,2), 
	IMPLIQ NUMBER(17,2), 
	MESLIQ NUMBER(10,0), 
	NROLIQ VARCHAR2(22 BYTE), 
	NROPAR NUMBER(10,0), 
	OPER VARCHAR2(8 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEMOVGASBAN"."ID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEMOVGASBAN"."ASIENTOEXPORTID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEMOVGASBAN"."ESTADONSL" IS 'No se exporta';
  
  DROP SEQUENCE  "LAPAMPA"."TEMP_SUEMOVIART_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEMOVIART_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEMOVIART";

CREATE TABLE "LAPAMPA"."TEMP_SUEMOVIART" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEMOVIART_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE), 	
    ANIOLIQ NUMBER(10,0), 
	CANTPER NUMBER(10,0), 
	CARATULA VARCHAR2(100 BYTE), 
	COMPN NUMBER(10,0), 
	ESTADO VARCHAR2(2 BYTE), 
	FECHALIQ DATE, 
	FECHAPRO DATE, 
	FINFUN NUMBER(10,0), 
	HORA NUMBER(10,0), 
	IDLIQ VARCHAR2(10 BYTE), 
	IMPBRUTO NUMBER(17,2), 
	IMPLIQ NUMBER(17,2), 
	MESLIQ NUMBER(10,0), 
	NROLIQ VARCHAR2(22 BYTE), 
	NROPAR NUMBER(10,0), 
	OPER VARCHAR2(8 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEMOVIART"."ID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEMOVIART"."ASIENTOEXPORTID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEMOVIART"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUEORDPAGC_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEORDPAGC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEORDPAGC";

  CREATE TABLE "LAPAMPA"."TEMP_SUEORDPAGC" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEORDPAGC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
    CODBAN NUMBER(10,0), 
	CODHAB VARCHAR2(2 BYTE), 
	CODIMP VARCHAR2(2 BYTE), 
	CPAG NUMBER(10,0), 
	CTALECOP NUMBER(15,0), 
	CTAPESOS NUMBER(15,0), 
	CTASIS NUMBER(10,0), 
	CTASISL NUMBER(10,0), 
	CTASUC NUMBER(10,0), 
	CTASUCL NUMBER(10,0), 
	CTATIPO NUMBER(10,0), 
	CTATIPOL NUMBER(10,0), 
	CUENTA NUMBER(10,0), 
	ESTPED VARCHAR2(2 BYTE), 
	FECHAENV DATE, 
	FECHAPED DATE, 
	LIQANIO NUMBER(10,0), 
	LIQMES NUMBER(10,0), 
	LIQTIPO VARCHAR2(2 BYTE), 
	MARCA VARCHAR2(2 BYTE), 
	NROPED NUMBER(10,0), 
	TOTBRUTO NUMBER(17,2), 
	TOTCRED NUMBER(17,2), 
	TOTCREDL NUMBER(17,2), 
	TOTNETO NUMBER(17,2), 
	TOTNETOL NUMBER(17,2), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEORDPAGC"."ID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEORDPAGC"."ASIENTOEXPORTID" IS 'No se exporta';
  COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEORDPAGC"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUEORDPAGD_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEORDPAGD_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEORDPAGD";

  CREATE TABLE "LAPAMPA"."TEMP_SUEORDPAGD" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEORDPAGD_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
 	CODHAB VARCHAR2(2 BYTE), 
	CUENTA NUMBER(10,0), 
	IMPBRUTO NUMBER(17,2), 
	IMPCRED NUMBER(17,2), 
	IMPCREDL NUMBER(17,2), 
	IMPNETO NUMBER(17,2), 
	IMPNETOL NUMBER(17,2), 
	JUR VARCHAR2(2 BYTE), 
	LIQANIO NUMBER(10,0), 
	LIQMES NUMBER(10,0), 
	LIQTIPO VARCHAR2(2 BYTE), 
	MARCALIS NUMBER(10,0), 
	NROLIQ VARCHAR2(22 BYTE), 
	NROPED NUMBER(10,0), 
	SEC NUMBER(10,0), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEORDPAGD"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEORDPAGD"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEORDPAGD"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUEPEMPRETC_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEPEMPRETC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEPEMPRETC";

  CREATE TABLE "LAPAMPA"."TEMP_SUEPEMPRETC" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEPEMPRETC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIOL NUMBER(10,0), 
	CARATU VARCHAR2(60 BYTE), 
	ESTADO VARCHAR2(2 BYTE), 
	FECHA DATE, 
	HORA NUMBER(10,0), 
	MESL NUMBER(10,0), 
	NMBR VARCHAR2(10 BYTE), 
	OPERA VARCHAR2(16 BYTE), 
	PASADO VARCHAR2(2 BYTE), 
	PCCANR NUMBER(10,0), 
	PCCARC NUMBER(10,0), 
	PCFECF NUMBER(10,0), 
	PCFECI NUMBER(10,0), 
	PCID NUMBER(10,0), 
	PCMONT NUMBER(17,2), 
	PCMOTC NUMBER(17,2), 
	PCPERI NUMBER(10,0), 
	TLIQ VARCHAR2(2 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPEMPRETC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPEMPRETC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPEMPRETC"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUEPEMPSUC_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEPEMPSUC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEPEMPSUC";

  CREATE TABLE "LAPAMPA"."TEMP_SUEPEMPSUC" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEPEMPSUC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIOL NUMBER(10,0), 
	CPAG NUMBER(10,0), 
	MESL NUMBER(10,0), 
	NMBR VARCHAR2(10 BYTE), 
	PRCUIL NUMBER(15,0), 
	SUCU NUMBER(10,0), 
	TLIQ VARCHAR2(2 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPEMPSUC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPEMPSUC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPEMPSUC"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUEPENSRETC_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEPENSRETC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEPENSRETC";

 CREATE TABLE "LAPAMPA"."TEMP_SUEPENSRETC" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEPENSRETC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
 	ANIOL NUMBER(10,0), 
	CARATU VARCHAR2(60 BYTE), 
	ESTADO VARCHAR2(2 BYTE), 
	FECHA DATE, 
	HORA NUMBER(10,0), 
	MESL NUMBER(10,0), 
	NMBR VARCHAR2(10 BYTE), 
	OPERA VARCHAR2(16 BYTE), 
	PASADO VARCHAR2(2 BYTE), 
	PCCANR NUMBER(10,0), 
	PCCARC NUMBER(10,0), 
	PCFECF NUMBER(10,0), 
	PCFECI NUMBER(10,0), 
	PCID NUMBER(10,0), 
	PCMONT NUMBER(17,2), 
	PCMOTC NUMBER(17,2), 
	PCPERI NUMBER(10,0), 
	TLIQ VARCHAR2(2 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPENSRETC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPENSRETC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPENSRETC"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUEPENSSUC_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEPENSSUC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEPENSSUC";

 CREATE TABLE "LAPAMPA"."TEMP_SUEPENSSUC" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEPENSSUC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIOL NUMBER(10,0), 
	MESL NUMBER(10,0), 
	NMBR VARCHAR2(10 BYTE), 
	PRCUIL NUMBER(15,0), 
	SUCU NUMBER(10,0), 
	TLIQ VARCHAR2(2 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPENSSUC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPENSSUC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPENSSUC"."ESTADONSL" IS 'No se exporta';
  
DROP SEQUENCE  "LAPAMPA"."TEMP_SUEPJCORETC_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEPJCORETC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEPJCORETC";

CREATE TABLE "LAPAMPA"."TEMP_SUEPJCORETC" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEPJCORETC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIOL NUMBER(10,0), 
	CARATU VARCHAR2(60 BYTE), 
	ESTADO VARCHAR2(2 BYTE), 
	FECHA DATE, 
	HORA NUMBER(10,0), 
	MESL NUMBER(10,0), 
	NMBR VARCHAR2(10 BYTE), 
	OPERA VARCHAR2(16 BYTE), 
	PASADO VARCHAR2(2 BYTE), 
	PCCANR NUMBER(10,0), 
	PCCARC NUMBER(10,0), 
	PCFECF NUMBER(10,0), 
	PCFECI NUMBER(10,0), 
	PCID NUMBER(10,0), 
	PCMONT NUMBER(17,2), 
	PCMOTC NUMBER(17,2), 
	PCPERI NUMBER(10,0), 
	TLIQ VARCHAR2(2 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPJCORETC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPJCORETC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPJCORETC"."ESTADONSL" IS 'No se exporta';
  
DROP SEQUENCE  "LAPAMPA"."TEMP_SUEPJCOSUC_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUEPJCOSUC_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUEPJCOSUC";

  CREATE TABLE "LAPAMPA"."TEMP_SUEPJCOSUC" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUEPJCOSUC_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIOL NUMBER(10,0), 
	MESL NUMBER(10,0), 
	NMBR VARCHAR2(10 BYTE), 
	PRCUIL NUMBER(15,0), 
	SUCU NUMBER(10,0), 
	TLIQ VARCHAR2(2 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPJCOSUC"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPJCOSUC"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUEPJCOSUC"."ESTADONSL" IS 'No se exporta';
  
DROP SEQUENCE  "LAPAMPA"."TEMP_SUETFCONCEPT_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUETFCONCEPT_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUETFCONCEPT";

  CREATE TABLE "LAPAMPA"."TEMP_SUETFCONCEPT" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUETFCONCEPT_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIOCPTO NUMBER(10,0), 
	CPTO NUMBER(10,0), 
	DESCRIP VARCHAR2(120 BYTE), 
	MESCPTO NUMBER(10,0), 
	TIPOCPTO VARCHAR2(6 BYTE), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFCONCEPT"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFCONCEPT"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFCONCEPT"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUETFDESCUEN_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUETFDESCUEN_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUETFDESCUEN";

  CREATE TABLE "LAPAMPA"."TEMP_SUETFDESCUEN" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUETFDESCUEN_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
 	ANIOLIQ NUMBER(10,0), 
	CODENTE NUMBER(10,0), 
	CODORI NUMBER(10,0), 
	CONVE NUMBER(10,0), 
	CONVENIO NUMBER(10,0), 
	CPTO NUMBER(10,0), 
	CUENTA NUMBER(10,0), 
	ENTRADA NUMBER(10,0), 
	ENVIO NUMBER(10,0), 
	FECHAORI DATE, 
	HAB VARCHAR2(2 BYTE), 
	IMPORTE NUMBER(17,2), 
	JUR VARCHAR2(2 BYTE), 
	MESLIQ NUMBER(10,0), 
	MONEDA NUMBER(10,0), 
	NROLIQ VARCHAR2(22 BYTE), 
	TIPOMOV NUMBER(10,0), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFDESCUEN"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFDESCUEN"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFDESCUEN"."ESTADONSL" IS 'No se exporta';

DROP SEQUENCE  "LAPAMPA"."TEMP_SUETFDETALLE_SEQ";

CREATE SEQUENCE  "LAPAMPA"."TEMP_SUETFDETALLE_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;

DROP TABLE "LAPAMPA"."TEMP_SUETFDETALLE";

  CREATE TABLE "LAPAMPA"."TEMP_SUETFDETALLE" 
   (ID NUMBER(19,0) DEFAULT "LAPAMPA"."TEMP_SUETFDETALLE_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	ASIENTOEXPORTID NUMBER(19,0) NOT NULL ENABLE,
	ESTADONSL VARCHAR2(20 BYTE),
	ANIOLIQ NUMBER(10,0), 
	BCOPAGO NUMBER(10,0), 
	CARATU VARCHAR2(120 BYTE), 
	CBU1 NUMBER(10,0), 
	CBU2 VARCHAR2(34 BYTE), 
	CIRC NUMBER(10,0), 
	CLIENTE NUMBER(15,0), 
	CODDOC NUMBER(10,0), 
	CODENTE NUMBER(10,0), 
	CODERROR NUMBER(10,0), 
	CODORI NUMBER(10,0), 
	CODPROV VARCHAR2(22 BYTE), 
	CONVE NUMBER(10,0), 
	CONVENIO NUMBER(10,0), 
	CPTO NUMBER(10,0), 
	CTADES NUMBER(15,0), 
	CTAORI NUMBER(15,0), 
	CUENTA NUMBER(10,0), 
	CUITPROV NUMBER(15,0), 
	ENTRADA NUMBER(10,0), 
	ENVIO NUMBER(10,0), 
	ESTADO VARCHAR2(2 BYTE), 
	EXPTE VARCHAR2(22 BYTE), 
	FECHAENV DATE, 
	FECHAORI DATE, 
	FECHAPAG DATE, 
	FORMAPAG NUMBER(10,0), 
	HAB VARCHAR2(2 BYTE), 
	IMPORTE NUMBER(17,2), 
	JUR VARCHAR2(2 BYTE), 
	JUZGA VARCHAR2(60 BYTE), 
	MARCALIS VARCHAR2(2 BYTE), 
	MESLIQ NUMBER(10,0), 
	MONEDA NUMBER(10,0), 
	NOMPROV VARCHAR2(120 BYTE), 
	NOMSUC VARCHAR2(40 BYTE), 
	NORDEN VARCHAR2(24 BYTE), 
	NRODOC NUMBER(15,0), 
	NROLIQ VARCHAR2(22 BYTE), 
	OBSER VARCHAR2(200 BYTE), 
	OFICIO VARCHAR2(20 BYTE), 
	PERIODO NUMBER(10,0), 
	SECRE VARCHAR2(6 BYTE), 
	SISDES NUMBER(10,0), 
	SISORI NUMBER(10,0), 
	SUCDES NUMBER(10,0), 
	SUCORI NUMBER(10,0), 
	SUCPAGO NUMBER(10,0), 
	SUCURSAL NUMBER(10,0), 
	TIPODES NUMBER(10,0), 
	TIPOMOV NUMBER(10,0), 
	TIPORI NUMBER(10,0), 
	MARCACOPIADO VARCHAR2(2 BYTE)
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFDETALLE"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFDETALLE"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."TEMP_SUETFDETALLE"."ESTADONSL" IS 'No se exporta';