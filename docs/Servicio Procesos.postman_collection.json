{"info": {"_postman_id": "9201be01-c968-45d1-84b1-261dec4c0d7d", "name": "<PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "14416183"}, "item": [{"name": "API v1", "item": [{"name": "Asientos Exports", "item": [{"name": "Listado Procesos", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"procesoId\": 22959 // null lista todos\r\n    //    \"estado\": \"Encolado\" // nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos/listadoProcesos", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos", "listadoProcesos"]}, "description": "Lista los proceso de asientos correspondientes a un id de liquidaciones procesos, si no hay los GENERA, este endpoint llena el combo de nombres de asientos en el front."}, "response": []}, {"name": "Listado Procesos Filtrado", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userLogin\": 1007, // not null\r\n    \"anio\": null, // nullable\r\n    \"mes\": null, // nullable\r\n    \"procesoId\": null, // null lista todos los names\r\n    \"nombre\": null // nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos/listarAsientosContablesFiltrado", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos", "listarAsientosContablesFiltrado"]}}, "response": []}, {"name": "Generar Procesos", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userLogin\": 1008, // not null\r\n    \"procesoId\": 23038, // not null\r\n    \"anio\": 2024, // nullable\r\n    \"mes\": 3, // nullable    \r\n    \"empresaId\": 3, // nullable\r\n    \"nombre\": \"ordpagd\" // not null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos/generarProcesos", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos", "generarProcesos"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos/listarTodos", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos", "listarTodos"]}}, "response": []}, {"name": "Listar <PERSON>mbre<PERSON>", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos/listarNombres", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos", "listarNombres"]}}, "response": []}, {"name": "ById", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos?id=393", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos"], "query": [{"key": "id", "value": "393"}]}}, "response": []}, {"name": "listarAsociados", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos/listarAsociados?id=374", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos", "listarAsociados"], "query": [{"key": "id", "value": "374"}]}}, "response": []}, {"name": "Listar Empresas", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/asientos/listarEmpresas?id=22959", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "asientos", "listarEmpresas"], "query": [{"key": "id", "value": "22959"}]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Generar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{// ESTO DEBERIA PODER COMPLETAR EL FORMULARIO DINAMICO\r\n    \"asientoExportId\": 15,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n    //\"habilitacionId\": 11, // VIALIDAD PROVINCIAL nullable\r\n    //\"jurisdiccionId\": 8244, // DIRECCIÓN PROVINCIAL DE VIALIDAD nullable\r\n    //\"empresaId\": 10, // DIRECC.PROV.DE VIALIDAD nullable\r\n   // \"reciboId\": 50884945 // nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/liquisus/generar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "liquisus", "generar"]}}, "response": []}, {"name": "Listar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 325 // null lista todos\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/liquisus/listar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "liquisus", "listar"]}}, "response": []}, {"name": "Bo<PERSON>r", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable PERO SIN EFECTO\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/liquisus/borrar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "liquisus", "borrar"]}}, "response": []}, {"name": "Exportar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/liquisus/exportar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "liquisus", "exportar"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"procesoId\": 21774,\r\n    \"asientoExportId\": 12\r\n    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/liquisus/hilos", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "liquisus", "hilos"]}}, "response": []}]}, {"name": "MovGasBan", "item": [{"name": "Generar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 117,\r\n    \"procesoId\": 23038\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TestServer}}/servicio-liq-procesos/api/v1/movgasban/generar", "host": ["{{TestServer}}"], "path": ["servicio-liq-procesos", "api", "v1", "movgasban", "generar"]}}, "response": []}, {"name": "Listar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 117 // null lista todos\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TestServer}}/servicio-liq-procesos/api/v1/movgasban/listar", "host": ["{{TestServer}}"], "path": ["servicio-liq-procesos", "api", "v1", "movgasban", "listar"]}}, "response": []}]}, {"name": "TFConcept", "item": [{"name": "Generar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{// ESTO DEBERIA PODER COMPLETAR EL FORMULARIO DINAMICO\r\n    \"asientoExportId\": 118,\r\n    \"procesoId\": 23038 // enero 2023 not nullable\r\n    //\"habilitacionId\": 11, // VIALIDAD PROVINCIAL nullable\r\n    //\"jurisdiccionId\": 8244, // DIRECCIÓN PROVINCIAL DE VIALIDAD nullable\r\n    //\"empresaId\": 10, // DIRECC.PROV.DE VIALIDAD nullable\r\n   // \"reciboId\": 50884945 // nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TestServer}}/servicio-liq-procesos/api/v1/tfconcept/generar", "host": ["{{TestServer}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfconcept", "generar"]}}, "response": []}, {"name": "Listar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 332 // null lista todos\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TestServer}}/servicio-liq-procesos/api/v1/tfconcept/listar", "host": ["{{TestServer}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfconcept", "listar"]}}, "response": []}, {"name": "Bo<PERSON>r", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable PERO SIN EFECTO\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfconcept/borrar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfconcept", "borrar"]}}, "response": []}, {"name": "Exportar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 1,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfconcept/exportar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfconcept", "exportar"]}}, "response": []}]}, {"name": "TFDescuent", "item": [{"name": "Generar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{// ESTO DEBERIA PODER COMPLETAR EL FORMULARIO DINAMICO\r\n    \"asientoExportId\": 98,\r\n    \"procesoId\": 23312 // enero 2023 not nullable\r\n    //\"habilitacionId\": 11, // VIALIDAD PROVINCIAL nullable\r\n    //\"jurisdiccionId\": 8244, // DIRECCIÓN PROVINCIAL DE VIALIDAD nullable\r\n    //\"empresaId\": 10, // DIRECC.PROV.DE VIALIDAD nullable\r\n   // \"reciboId\": 50884945 // nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdescuen/generar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdescuen", "generar"]}}, "response": []}, {"name": "Listar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 119 // null lista todos\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdescuen/listar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdescuen", "listar"]}}, "response": []}, {"name": "Bo<PERSON>r", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable PERO SIN EFECTO\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdescuen/borrar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdescuen", "borrar"]}}, "response": []}, {"name": "Exportar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 1,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdescuen/exportar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdescuen", "exportar"]}}, "response": []}]}, {"name": "TFDetalle", "item": [{"name": "Generar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{// ESTO DEBERIA PODER COMPLETAR EL FORMULARIO DINAMICO\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n    //\"habilitacionId\": 11, // VIALIDAD PROVINCIAL nullable\r\n    //\"jurisdiccionId\": 8244, // DIRECCIÓN PROVINCIAL DE VIALIDAD nullable\r\n    //\"empresaId\": 10, // DIRECC.PROV.DE VIALIDAD nullable\r\n   // \"reciboId\": 50884945 // nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdetalle/generar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdetalle", "generar"]}}, "response": []}, {"name": "Listar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 120, // null lista todos\r\n    \"procesoId\": null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdetalle/listar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdetalle", "listar"]}}, "response": []}, {"name": "Bo<PERSON>r", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable PERO SIN EFECTO\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdetalle/borrar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdetalle", "borrar"]}}, "response": []}, {"name": "Exportar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 1,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/tfdetalle/exportar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "tfdetalle", "exportar"]}}, "response": []}]}, {"name": "Ordpagc", "item": [{"name": "Generar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{// ESTO DEBERIA PODER COMPLETAR EL FORMULARIO DINAMICO\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n    //\"habilitacionId\": 11, // VIALIDAD PROVINCIAL nullable\r\n    //\"jurisdiccionId\": 8244, // DIRECCIÓN PROVINCIAL DE VIALIDAD nullable\r\n    //\"empresaId\": 10, // DIRECC.PROV.DE VIALIDAD nullable\r\n   // \"reciboId\": 50884945 // nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/ordpagc/generar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagc", "generar"]}}, "response": []}, {"name": "Listar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 120, // null lista todos\r\n    \"procesoId\": null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TestServer}}/servicio-liq-procesos/api/v1/ordpagc/listar", "host": ["{{TestServer}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagc", "listar"]}}, "response": []}, {"name": "Bo<PERSON>r", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable PERO SIN EFECTO\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/ordpagc/borrar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagc", "borrar"]}}, "response": []}, {"name": "Exportar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 1,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/ordpagc/exportar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagc", "exportar"]}}, "response": []}]}, {"name": "Ordpagd", "item": [{"name": "Generar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"empresaId\": \"3\",\r\n    \"asientoExportId\": 393,\r\n    \"procesoId\": 23038\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/ordpagd/generar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagd", "generar"]}}, "response": []}, {"name": "Listar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 393, // not null\r\n    \"procesoId\": null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/ordpagd/listar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagd", "listar"]}}, "response": []}, {"name": "Bo<PERSON>r", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 12,\r\n    \"procesoId\": 21774 // enero 2023 not nullable PERO SIN EFECTO\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/ordpagd/borrar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagd", "borrar"]}}, "response": []}, {"name": "Exportar", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"asientoExportId\": 1,\r\n    \"procesoId\": 21774 // enero 2023 not nullable\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-liq-procesos/api/v1/ordpagd/exportar", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "api", "v1", "ordpagd", "exportar"]}}, "response": []}]}]}, {"name": "Checkers", "item": [{"name": "Check status", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-liq-procesos/estado", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "estado"]}}, "response": []}, {"name": "Logs Test", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-liq-procesos/logsTest", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "logsTest"]}}, "response": []}]}, {"name": "Docs", "item": [{"name": "Api-Docs", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-liq-procesos/v3/api-docs", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "v3", "api-docs"]}}, "response": []}, {"name": "Swagger", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-liq-procesos/swagger-ui/index.html", "host": ["{{Server}}"], "path": ["servicio-liq-procesos", "swagger-ui", "index.html"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "Server", "value": "localhost:8080", "type": "string"}, {"key": "DesaServer", "value": "https://desannslapi.lapampa.gob.ar", "type": "string"}, {"key": "TestServer", "value": "https://testnnslapi.lapampa.gob.ar", "type": "string"}, {"key": "ProdServer", "value": "https://nnslapi.lapampa.gob.ar", "type": "string"}]}