DROP SEQUENCE  "LAPAMPA"."SUE_ORDPAGD_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_ORDPAGD_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_ORDPAGD" 
CREATE TABLE "LAPAMPA"."SUE_ORDPAGD" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_ORDPAGD_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"CODHAB" VARCHAR2(2 BYTE), 
	"CUENTA" NUMBER(10,0), 
	"IMPBRUTO" NUMBER(17,2), 
	"IMPCRED" NUMBER(17,2), 
	"IMPCREDL" NUMBER(17,2), 
	"IMPNETO" NUMBER(17,2), 
	"IMPNETOL" NUMBER(17,2), 
	"JUR" VARCHAR2(2 BYTE), 
	"LIQANIO" NUMBER(10,0), 
	"LIQMES" NUMBER(10,0), 
	"LIQTIPO" VARCHAR2(2 BYTE), 
	"MARCALIS" NUMBER(10,0), 
	"NROLIQ" VARCHAR2(22 BYTE), 
	"NROPED" NUMBER(10,0), 
	"SEC" NUMBER(10,0), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	 CONSTRAINT "SUE_ORDPAGD_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 0 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
  
   COMMENT ON COLUMN "LAPAMPA"."SUE_ORDPAGD"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_ORDPAGD"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_ORDPAGD"."ESTADONSL" IS 'No se exporta';

