DROP SEQUENCE  "LAPAMPA"."SUE_MOVIART_SEQ"
CREATE SEQUENCE  "LAPAMPA"."SUE_MOVIART_SEQ"  MINVALUE 1 MAXVALUE 9999999999 INCREMENT BY 1 START WITH 1 NOCACHE  ORDER  NOCYCLE  NOKEEP  NOSCALE  GLOBAL;
DROP TABLE "LAPAMPA"."SUE_MOVIART"
CREATE TABLE "LAPAMPA"."SUE_MOVIART" 
   (	"ID" NUMBER(19,0) DEFAULT "LAPAMPA"."SUE_MOVIART_SEQ"."NEXTVAL" NOT NULL ENABLE, 
	"ASIENTOEXPORTID" NUMBER(19,0) NOT NULL ENABLE, 
	"ESTADONSL" VARCHAR2(20 BYTE), 
	"ANIOLIQ" NUMBER(10,0), 
	"CANTPER" NUMBER(10,0), 
	"CARATULA" VARCHAR2(100 BYTE), 
	"COMPN" NUMBER(10,0), 
	"ESTADO" VARCHAR2(2 BYTE), 
	"FECHALIQ" DATE, 
	"FECHAPRO" DATE, 
	"FINFUN" NUMBER(10,0), 
	"HORA" NUMBER(10,0), 
	"IDLIQ" VARCHAR2(10 BYTE), 
	"IMPBRUTO" NUMBER(17,2), 
	"IMPLIQ" NUMBER(17,2), 
	"MESLIQ" NUMBER(10,0), 
	"NROLIQ" VARCHAR2(22 BYTE), 
	"NROPAR" NUMBER(10,0), 
	"OPER" VARCHAR2(8 BYTE), 
	"MARCACOPIADO" VARCHAR2(2 BYTE), 
	 CONSTRAINT "SUE_MOVIART_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 0 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "LAPAMPA"."SUE_MOVIART"."ID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_MOVIART"."ASIENTOEXPORTID" IS 'No se exporta';
   COMMENT ON COLUMN "LAPAMPA"."SUE_MOVIART"."ESTADONSL" IS 'No se exporta';

