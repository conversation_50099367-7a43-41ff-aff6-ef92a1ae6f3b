<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.7</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>
	<groupId>ar.gob.lapampa.nsl</groupId>
	<artifactId>servicio-procesos</artifactId>
	<version>1.2.0</version>
	<name>servicio-procesos</name>
	<description>Servicio de Liquidación Procesos</description>
	<properties>
		<java.version>21</java.version>
		<org.mapstruct.version>1.6.2</org.mapstruct.version>
		<lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
		<lombokSource>${project.basedir}/src/main/java/</lombokSource>
		<maven.javadoc.skip>true</maven.javadoc.skip>
		<spring-cloud.version>2024.0.1</spring-cloud.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web-services</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>net.ttddyy</groupId>
			<artifactId>datasource-proxy</artifactId>
			<version>1.10</version>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc11</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${org.mapstruct.version}</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-netflix-eureka-client -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-config-client -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-config-client</artifactId>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-bootstrap -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>ar.gob.lapampa.nsl.recursos</groupId>
			<artifactId>nsl-recursos-compartidos</artifactId>
			<version>2.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<!--
		https://mvnrepository.com/artifact/org.springdoc/springdoc-openapi-starter-webmvc-ui -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.9</version>
		</dependency>
		<!-- QueryDSL -->
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-jpa</artifactId>
			<classifier>jakarta</classifier>
			<version>${querydsl.version}</version>
		</dependency>
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-apt</artifactId>
			<classifier>jakarta</classifier>
			<version>${querydsl.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20240303</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>com.vaadin.external.google</groupId>
					<artifactId>android-json</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<profiles>
		<profile>
			<id>dlombok</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.projectlombok</groupId>
						<artifactId>lombok-maven-plugin</artifactId>
						<version>*********</version>
						<executions>
							<execution>
								<phase>generate-sources</phase>
								<goals>
									<goal>delombok</goal>
								</goals>
							</execution>
						</executions>
						<configuration>
							<sourceDirectory>${lombokSource}</sourceDirectory>
							<verbose>true</verbose>
							<formatPreferences>
								<generateDelombokComment>skip</generateDelombokComment>
								<javaLangAsFQN>skip</javaLangAsFQN>
								<suppressWarnings>skip</suppressWarnings>
							</formatPreferences>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
	<build>
		<finalName>servicio-procesos</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>io.github.git-commit-id</groupId>
				<artifactId>git-commit-id-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>get-the-git-infos</id>
						<goals>
							<goal>revision</goal>
						</goals>
						<phase>initialize</phase>
					</execution>
				</executions>
				<configuration>
					<generateGitPropertiesFile>true</generateGitPropertiesFile>
					<generateGitPropertiesFilename>
						${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
					<includeOnlyProperties>
						<includeOnlyProperty>^git.commit.time$</includeOnlyProperty>
						<includeOnlyProperty>^git.commit.id.full$</includeOnlyProperty>
						<includeOnlyProperty>^git.commit.user.name$</includeOnlyProperty>
						<includeOnlyProperty>^git.branch$</includeOnlyProperty>
					</includeOnlyProperties>
					<commitIdGenerationMode>full</commitIdGenerationMode>
					<injectAllReactorProjects>true</injectAllReactorProjects>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>build-info</goal>
						</goals>
						<configuration>
							<additionalProperties>
								<Tab.Category>Worker</Tab.Category>
								<Tab.Type>HikariCP</Tab.Type>
							</additionalProperties>
							<excludeInfoProperties>
								<infoProperty>name</infoProperty>
							</excludeInfoProperties>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>${lombok.mapstruct.binding.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<!-- QueryDSL plugin -->
			<plugin>
				<groupId>com.mysema.maven</groupId>
				<artifactId>apt-maven-plugin</artifactId>
				<version>1.1.3</version>
				<executions>
					<execution>
						<goals>
							<goal>process</goal>
						</goals>
						<configuration>
							<outputDirectory>target/generated-sources/java</outputDirectory>
							<processor>
								com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>properties</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<argLine>-javaagent:${net.bytebuddy:byte-buddy-agent:jar}</argLine>
				</configuration>
			</plugin>
			<!--<plugin>
				<groupId>org.graalvm.buildtools</groupId>
				<artifactId>native-maven-plugin</artifactId>
			</plugin>-->
		</plugins>
	</build>
</project>
