{"AsientoDescriptor": {"exportarURL": "//api//v1//pempsuc//exportar", "generalName": "Plan de Empleo Sucursales", "generarURL": "//api//v1//pempsuc//generar", "tableName": "pemps<PERSON>", "asociado": null}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLACK", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Cuenta Bancaria", "name": "Cuenta Bancaria", "order": 2, "paramName": "ctaban", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Cuenta del Asiento", "name": "C<PERSON><PERSON>", "order": 3, "paramName": "cuenta", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Fecha del Asiento", "name": "<PERSON><PERSON>", "order": 4, "paramName": "fecha", "valueType": "DATE"}, {"color": "BLACK", "comment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "order": 5, "paramName": "fecori", "valueType": "DATE"}, {"color": "BLACK", "comment": "Nro. de Liquidación", "name": "Liquidación Nro.", "order": 6, "paramName": "liqnro", "valueType": "STRING"}, {"color": "BLACK", "comment": "Importe en Pesos", "name": "Importe", "order": 7, "paramName": "importe", "valueType": "MONEY"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 8, "paramName": "status", "valueType": "STRING"}]}