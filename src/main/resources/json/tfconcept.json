{"AsientoDescriptor": {"exportarURL": "//api//v1//tfconcept//exportar", "generalName": "T.F. <PERSON>", "generarURL": "//api//v1//tfconcept//generar", "tableName": "tfconcept", "asociado": null}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLUE", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "order": 2, "paramName": "anioCpto", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "order": 3, "paramName": "mesCpto", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Código", "name": "Código", "order": 4, "paramName": "cpto", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Concepto", "name": "Concepto", "order": 5, "paramName": "descrip", "valueType": "STRING"}, {"color": "BLACK", "comment": "Tipo de Concepto", "name": "Tipo", "order": 6, "paramName": "tipoCpto", "valueType": "STRING"}]}