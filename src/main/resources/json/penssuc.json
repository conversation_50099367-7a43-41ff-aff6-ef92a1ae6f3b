{"AsientoDescriptor": {"exportarURL": "//api//v1//penssuc//exportar", "generalName": "Pensiones Sucursales", "generarURL": "//api//v1//penssuc//generar", "tableName": "penssuc", "asociado": null}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}, {"comment": "OBLIGATORIO.FechaHasta", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "<PERSON><PERSON>", "paramName": "fecha<PERSON>asta", "valueType": "DATE"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLACK", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Año de Liquidación", "name": "<PERSON><PERSON>", "order": 2, "paramName": "aniol", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Mes de Liquidación", "name": "<PERSON><PERSON>", "order": 3, "paramName": "mesl", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Nombre del Asiento", "name": "Nombre", "order": 4, "paramName": "nmbr", "valueType": "STRING"}, {"color": "BLACK", "comment": "<PERSON><PERSON> <PERSON> persona", "name": "<PERSON><PERSON>", "order": 5, "paramName": "prcuil", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Sucursal de pago", "name": "Sucursal", "order": 6, "paramName": "sucu", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Tipo de Liquidación", "name": "Tipo Liq.", "order": 7, "paramName": "tliq", "valueType": "STRING"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 8, "paramName": "status", "valueType": "STRING"}]}