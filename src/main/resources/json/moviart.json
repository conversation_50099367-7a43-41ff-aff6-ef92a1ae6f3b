{"AsientoDescriptor": {"exportarURL": "//api//v1//moviart//exportar", "generalName": "Movimientos ART", "generarURL": "//api//v1//moviart//generar", "tableName": "moviart", "asociado": null}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLUE", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Caratula", "name": "Caratula", "order": 2, "paramName": "caratula", "valueType": "STRING"}, {"color": "BLACK", "comment": "Fecha de liquidado", "name": "<PERSON><PERSON>", "order": 3, "paramName": "fechaLiq", "valueType": "DATE"}, {"color": "BLACK", "comment": "Fecha de procesado", "name": "<PERSON><PERSON>", "order": 4, "paramName": "fechaPro", "valueType": "DATE"}, {"color": "BLACK", "comment": "Fin función", "name": "Fin Fun", "order": 5, "paramName": "finFun", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Importe de Liquidación en pesos", "name": "Importe Liquidación", "order": 6, "paramName": "impLiq", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Nro de liquidación", "name": "Nro Liq.", "order": 7, "paramName": "nroLiq", "valueType": "STRING"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 8, "paramName": "status", "valueType": "STRING"}]}