{"AsientoDescriptor": {"exportarURL": "//api//v1//pensretc//exportar", "generalName": "Pensiones Retornos", "generarURL": "//api//v1//pensretc//generar", "tableName": "pensretc", "asociado": "penssuc"}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}, {"comment": "OBLIGATORIO.FechaHasta", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "<PERSON><PERSON>", "paramName": "fecha<PERSON>asta", "valueType": "DATE"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLACK", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Caratula", "name": "Caratula", "order": 2, "paramName": "caratu", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Nombre", "name": "Nombre", "order": 3, "paramName": "nmbr", "valueType": "STRING"}, {"color": "BLACK", "comment": "Fecha del Asiento", "name": "<PERSON><PERSON>", "order": 4, "paramName": "fecha", "valueType": "DATE"}, {"color": "BLACK", "comment": "Pc monto", "name": "<PERSON> <PERSON>", "order": 5, "paramName": "pcmont", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Pc peri", "name": "PC <PERSON><PERSON>", "order": 6, "paramName": "pcperi", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Tipo liquidación", "name": "Tipo Liq.", "order": 7, "paramName": "tliq", "valueType": "STRING"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 8, "paramName": "status", "valueType": "STRING"}]}