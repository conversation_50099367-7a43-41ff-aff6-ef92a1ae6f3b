{"AsientoDescriptor": {"exportarURL": "//api//v1//ordpagd//exportar", "generalName": "Pedido de Fondos", "generarURL": "//api//v1//ordpagd//generar", "tableName": "ordpagd", "asociado": "ordpagc"}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}, {"comment": "OBLIGATORIO.Empresa Id", "defaultValue": "", "endPoint": "servicio-liq-procesos/api/v1/asientos/listarEmpresas?id=", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Empresa", "paramName": "empresaId", "valueType": "NUMBER"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLUE", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Código Habilitación", "name": "Cod. Hab.", "order": 2, "paramName": "codHab", "valueType": "STRING"}, {"color": "BLACK", "comment": "C<PERSON><PERSON>", "name": "C<PERSON><PERSON>", "order": 3, "paramName": "imp<PERSON><PERSON><PERSON>", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Importe cred", "name": "<PERSON><PERSON><PERSON>.", "order": 4, "paramName": "impCred", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Importe neto", "name": "Imp<PERSON>", "order": 5, "paramName": "impNeto", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Nro. de Liquidación", "name": "Liquidación Nro.", "order": 6, "paramName": "nroLiq", "valueType": "STRING"}, {"color": "BLACK", "comment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "order": 7, "paramName": "jur", "valueType": "STRING"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 8, "paramName": "status", "valueType": "STRING"}]}