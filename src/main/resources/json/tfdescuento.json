{"AsientoDescriptor": {"exportarURL": "//api//v1//tfdescuen//exportar", "generalName": "T.F. <PERSON>cuentos", "generarURL": "//api//v1//tfdescuen//generar", "tableName": "tfdescuen", "asociado": null}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLUE", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Numero Convenio", "name": "Convenio", "order": 2, "paramName": "convenio", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Importe en pesos", "name": "Importe", "order": 3, "paramName": "importe", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Año de liquidación", "name": "<PERSON><PERSON>", "order": 4, "paramName": "anioLiq", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Mes de Liquidación", "name": "<PERSON><PERSON>", "order": 5, "paramName": "mesLiq", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Concepto", "name": "Concepto", "order": 6, "paramName": "cpto", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Código de Jurisdicción", "name": "Jurisdicción", "order": 7, "paramName": "jur", "valueType": "STRING"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 8, "paramName": "status", "valueType": "STRING"}]}