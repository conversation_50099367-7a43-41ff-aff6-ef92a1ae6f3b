{"AsientoDescriptor": {"exportarURL": "//api//v1//tfdetalle//exportar", "generalName": "<PERSON><PERSON><PERSON><PERSON>", "generarURL": "//api//v1//tfdetalle//generar", "tableName": "tfdetalle", "asociado": null}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLUE", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "CBU", "name": "CBU", "order": 2, "paramName": "cbu1", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Cliente", "name": "Cliente", "order": 3, "paramName": "cliente", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "order": 4, "paramName": "fecha<PERSON><PERSON>", "valueType": "DATE"}, {"color": "BLACK", "comment": "Expediente", "name": "Expediente", "order": 5, "paramName": "expte", "valueType": "STRING"}, {"color": "BLACK", "comment": "Importe", "name": "Importe", "order": 6, "paramName": "importe", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Caratula", "name": "Caratula", "order": 7, "paramName": "caratu", "valueType": "STRING"}, {"color": "BLACK", "comment": "Número de Liquidación", "name": "Nro. Liq.", "order": 8, "paramName": "nroLiq", "valueType": "STRING"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 9, "paramName": "status", "valueType": "STRING"}]}