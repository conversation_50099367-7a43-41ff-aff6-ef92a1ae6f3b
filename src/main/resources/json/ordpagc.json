{"AsientoDescriptor": {"exportarURL": "//api//v1//ordpagc//exportar", "generalName": "Pedido de Fondos Cabeceras", "generarURL": "//api//v1//ordpagc//generar", "tableName": "ordpagc", "asociado": null}, "FieldParameters": [{"comment": "REPETIDO.Asiento Export Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": false, "name": "Asiento", "paramName": "asientoExportId", "valueType": "NUMBER"}, {"comment": "REPETIDO.Proceso Id", "defaultValue": "", "endPoint": "", "endPointRequest": "", "isInput": true, "isNullable": true, "name": "Proceso", "paramName": "procesoId", "valueType": "NUMBER"}, {"comment": "OBLIGATORIO.Empresa Id", "defaultValue": "", "endPoint": "servicio-parametrica-rrhh/api/v1/app/rrhhempresas/listar", "endPointRequest": "{\"activo\":true,\"sortBy\":\"codigo\",\"asc\": true }", "isInput": true, "isNullable": false, "name": "Empresa", "paramName": "empresaId", "valueType": "NUMBER"}], "HiddenColumns": ["id", "asientoExportId", "ma<PERSON><PERSON><PERSON><PERSON>", "status"], "TableHeaders": [{"color": "BLACK", "comment": "ID de registro", "name": "Código", "order": 1, "paramName": "id", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Cuenta en Pesos", "name": "Cuenta Pesos", "order": 2, "paramName": "ctaPesos", "valueType": "NUMBER"}, {"color": "BLACK", "comment": "Total Bruto", "name": "Total Bruto", "order": 3, "paramName": "totBruto", "valueType": "MONEY"}, {"color": "BLACK", "comment": "Total Neto", "name": "Total Neto", "order": 4, "paramName": "totNeto", "valueType": "MONEY"}, {"color": "BLACK", "comment": "<PERSON><PERSON>edido", "name": "<PERSON><PERSON>", "order": 5, "paramName": "fechaPed", "valueType": "DATE"}, {"color": "BLACK", "comment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "order": 6, "paramName": "fechaEnv", "valueType": "DATE"}, {"color": "BLACK", "comment": "Número de pedido", "name": "Nro. Pedido", "order": 7, "paramName": "nroPed", "valueType": "NUMBER"}, {"color": "BLUE", "comment": "Estado del Registro", "name": "Estado", "order": 8, "paramName": "status", "valueType": "STRING"}]}