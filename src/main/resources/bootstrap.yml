application: Servicio Procesos
config-origin: Service yml file
environment: ${RUNTIME_ENVIRONMENT:none}
eureka:
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${random.uuid}
management:
  endpoint:
    configprops:
      show-values: ALWAYS
    env:
      post:
        enabled: true
      show-values: ALWAYS
    health:
      show-details: ALWAYS
    quartz:
      show-values: ALWAYS
    refresh:
      enabled: true
    restart:
      enabled: false
    shutdown:
      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
  health:
    db:
      enabled: false
project:
  description: '@project.description@'
  version: '@project.version@'
server:
  port: 0
spring:
  application:
    name: servicio-liq-procesos
  cloud:
    compatibility-verifier:
      enabled: true
    config:
      bus:
        enabled: true
        refresh:
          enabled: true
      uri: http://localhost:8888
  datasource:
    url: jdbc:oracle:thin:@*********:1521/DESA
    username: LAPAMPA
    password: rjqUsKi2mxwog74P4VLN
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      connection-timeout: 20000
      minimum-idle: 1
      maximum-pool-size: 5
      idle-timeout: 60000
      max-lifetime: 120000
      leak-detection-threshold: 90000 # 90 hasta segundos de conexion idle sin cerrar
      connection-test-query: SELECT 1 FROM DUAL
      pool-name: HikariPool-procesos
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    generate-ddl: false # OJO CON ESTE PARAMETRO
  profiles:
    active: ${RUNTIME_ENVIRONMENT:desa}
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    config-url: /${spring.application.name}/v3/api-docs/swagger-config
    disable-swagger-default-url: true
    doc-expansion: none
    path: /swagger-ui
    url: /${spring.application.name}/v3/api-docs
---
spring:
  config:
    activate:
      on-profile: local
  datasource:
    url: ***********************************
    username: LAPAMPA
    password: rjqUsKi2mxwog74P4VLN
  banner:
    location: classpath:banner_local.txt
logging:
  level:
    root: INFO
---
spring:
  config:
    activate:
      on-profile: desa
  datasource:
    url: jdbc:oracle:thin:@*********:1521/DESA
    username: LP_ADMIN
    password: Cesida#2023
  banner:
    location: classpath:banner.txt
logging:
  level:
    root: ERROR
---
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:oracle:thin:@*********:1521/TEST
    username: LP_ADMIN
    password: Cesida#2023
  banner:
    location: classpath:banner_test.txt
logging:
  level:
    root: ERROR
---
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: *****************************************
    username: LP_ADMIN
    password: Cesida#2023
  banner:
    location: classpath:banner_prod.txt
logging:
  level:
    root: ERROR