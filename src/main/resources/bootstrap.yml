application: Servicio Procesos
config-origin: Service yml file
environment: ${RUNTIME_ENVIRONMENT:none}
eureka:
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${random.uuid}
management:
  endpoint:
    configprops:
      show-values: ALWAYS
    env:
      post:
        enabled: true
      show-values: ALWAYS
    health:
      show-details: ALWAYS
    quartz:
      show-values: ALWAYS
    refresh:
      enabled: true
    restart:
      enabled: false
    shutdown:
      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
  health:
    db:
      enabled: false
project:
  description: '@project.description@'
  version: '@project.version@'
server:
  port: 0
spring:
  application:
    name: servicio-liq-procesos
  cloud:
    compatibility-verifier:
      enabled: true
    config:
      bus:
        enabled: true
        refresh:
          enabled: true
      uri: http://localhost:8888
  datasource:
    oracle:
      url: '*************************************'
      username: LAPAMPA
      password: rjqUsKi2mxwog74P4VLN
      driver-class-name: oracle.jdbc.OracleDriver
      hikari:
        connection-timeout: 20000
        minimum-idle: 5
        maximum-pool-size: 12
        idle-timeout: 600000
        max-lifetime: 1200000
        leak-detection-threshold: 90000
        connection-test-query: SELECT 1 FROM DUAL
        pool-name: HikariPool-oracle-administrador
    progress:
      # Configuración real de Progress OpenEdge
      jdbc-url: '*****************************************************************'
      username: UsrSueldos
      password: 'DesaTest'
      driver-class-name: com.ddtek.jdbc.openedge.OpenEdgeDriver
      hikari:
        connection-timeout: 20000
        minimum-idle: 3
        maximum-pool-size: 8
        idle-timeout: 600000
        max-lifetime: 1200000
        leak-detection-threshold: 90000
        connection-test-query: SELECT 1 FROM sysprogress.syscalctable
        pool-name: HikariPool-progress-administrador
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    generate-ddl: false # OJO CON ESTE PARAMETRO
  profiles:
    active: ${RUNTIME_ENVIRONMENT:desa}
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    config-url: /${spring.application.name}/v3/api-docs/swagger-config
    disable-swagger-default-url: true
    doc-expansion: none
    path: /swagger-ui
    url: /${spring.application.name}/v3/api-docs
---
spring:
  config:
    activate:
      on-profile: local
  datasource:
    oracle:
      url: '*************************************'
      username: LAPAMPA
      password: rjqUsKi2mxwog74P4VLN
      driver-class-name: oracle.jdbc.OracleDriver
    progress:
      # Configuración real de Progress OpenEdge
      jdbc-url: '*****************************************************************'
      username: UsrSueldos
      password: 'DesaTest'
      driver-class-name: com.ddtek.jdbc.openedge.OpenEdgeDriver
      hikari:
        connection-timeout: 20000
        minimum-idle: 3
        maximum-pool-size: 8
        idle-timeout: 600000
        max-lifetime: 1200000
        leak-detection-threshold: 90000
        connection-test-query: SELECT 1 FROM sysprogress.syscalctable
        pool-name: HikariPool-progress-local
  banner:
    location: classpath:banner_local.txt
logging:
  level:
    root: INFO
---
spring:
  config:
    activate:
      on-profile: desa
  datasource:
    oracle:
      url: '*************************************'
      username: LP_ADMIN
      password: 'Cesida#2023'
      driver-class-name: oracle.jdbc.OracleDriver
    progress:
      # Configuración real de Progress OpenEdge
      url: '*****************************************************************'
      username: UsrSueldos
      password: 'DesaTest'
      driver-class-name: com.ddtek.jdbc.openedge.OpenEdgeDriver
  banner:
    location: classpath:banner.txt
logging:
  level:
    root: ERROR
---
spring:
  config:
    activate:
      on-profile: test
  datasource:
    oracle:
      url: '*************************************'
      username: LP_ADMIN
      password: 'Cesida#2023'
      driver-class-name: oracle.jdbc.OracleDriver
    progress:
      # Configuración real de Progress OpenEdge - Test
      jdbc-url: '*******************************************************************'
      username: UsrSueldos
      password: 'DesaTest'
      driver-class-name: com.ddtek.jdbc.openedge.OpenEdgeDriver
      hikari:
        connection-timeout: 20000
        minimum-idle: 3
        maximum-pool-size: 8
        idle-timeout: 600000
        max-lifetime: 1200000
        leak-detection-threshold: 90000
        connection-test-query: SELECT 1 FROM sysprogress.syscalctable
        pool-name: HikariPool-progress-test
  banner:
    location: classpath:banner_test.txt
logging:
  level:
    root: ERROR
---
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    oracle:
      url: '*****************************************'
      username: LP_ADMIN
      password: 'Cesida#2023'
      driver-class-name: oracle.jdbc.OracleDriver
    progress:
      # Configuración real de Progress OpenEdge - Producción (real aun no suministrada)
      jdbc-url: '*************************************************************************'
      username: progress_admin
      password: 'Progress#2023'
      driver-class-name: com.ddtek.jdbc.openedge.OpenEdgeDriver
      hikari:
        connection-timeout: 20000
        minimum-idle: 5
        maximum-pool-size: 15
        idle-timeout: 600000
        max-lifetime: 1200000
        leak-detection-threshold: 90000
        connection-test-query: SELECT 1 FROM sysprogress.syscalctable
        pool-name: HikariPool-progress-prod
  banner:
    location: classpath:banner_prod.txt
logging:
  level:
    root: ERROR