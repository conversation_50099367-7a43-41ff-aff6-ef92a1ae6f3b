package ar.gob.lapampa.nsl.servicioliqprocesos.mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.RrhhEmpresaDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.RrhhEmpresa;

public class RrrhhEmpresaMapper {

  private RrrhhEmpresaMapper() {}

  public static RrhhEmpresaDTO

      toRrhhEmpresaDTO(RrhhEmpresa empresa) {

    if (empresa == null) {
      return null;
    }

    RrhhEmpresaDTO rrhhEmpresaDTO = new RrhhEmpresaDTO();

    rrhhEmpresaDTO.setId(empresa.getId());
    rrhhEmpresaDTO.setDescripcion(empresa.getDescripcion());
    rrhhEmpresaDTO.setCodigo(empresa.getCodigo());
    rrhhEmpresaDTO.setActivo(empresa.getActivo());

    return rrhhEmpresaDTO;
  }

  public static List<RrhhEmpresaDTO> toRrhhEmpresaDTOList(List<RrhhEmpresa> list) {
    if (list == null) {
      return Collections.emptyList();
    }

    List<RrhhEmpresaDTO> list1 = new ArrayList<>(list.size());
    for (RrhhEmpresa rrhhEmpresa : list) {
      list1.add(toRrhhEmpresaDTO(rrhhEmpresa));
    }
    return list1;
  }

  public static RrhhEmpresa

      toRrhhEmpresa(RrhhEmpresaDTO rrhhEmpresaDTO) {

    if (rrhhEmpresaDTO == null) {
      return null;
    }

    RrhhEmpresa rrhhEmpresa = new RrhhEmpresa();

    rrhhEmpresa.setId(rrhhEmpresaDTO.getId());
    rrhhEmpresa.setDescripcion(rrhhEmpresaDTO.getDescripcion());
    rrhhEmpresa.setCodigo(rrhhEmpresaDTO.getCodigo());
    rrhhEmpresa.setActivo(rrhhEmpresaDTO.getActivo());

    return rrhhEmpresa;
  }

  public static List<RrhhEmpresa> toRrhhEmpresaList(List<RrhhEmpresaDTO> list) {
    if (list == null) {
      return Collections.emptyList();
    }

    List<RrhhEmpresa> list1 = new ArrayList<>(list.size());
    for (RrhhEmpresaDTO rrhhEmpresaDTO : list) {
      list1.add(toRrhhEmpresa(rrhhEmpresaDTO));
    }
    return list1;
  }
}

