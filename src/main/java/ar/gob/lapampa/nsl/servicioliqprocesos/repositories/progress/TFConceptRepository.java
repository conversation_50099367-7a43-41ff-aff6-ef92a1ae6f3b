package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.progress;

import ar.gob.lapampa.nsl.servicioliqprocesos.entities.progress.TFConcept;
import java.util.List;

import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * PROGRESS
 * Repositorio para la entidad TFConcept (tabla SUETFCONCEPT)
 */
@Repository
public interface TFConceptRepository extends JpaRepository<TFConcept, TFConcept.ConceptoSueldoId> {

    /**
     * Buscar conceptos por año
     */
    List<TFConcept> findByAnioCptoOrderByCpto(Integer anioCpto);

    /**
     * Buscar conceptos por tipo
     */
    List<TFConcept> findByTipoCptoOrderByAnioCptoDescCpto(String tipoCpto);

    /**
     * Buscar conceptos por año y tipo
     */
    List<TFConcept> findByAnioCptoAndTipoCptoOrderByCpto(Integer anioCpto, String tipoCpto);

    /**
     * Buscar conceptos que contengan texto en la descripción
     */
    @Query("SELECT c FROM TFConcept c WHERE UPPER(c.descrip) LIKE UPPER(CONCAT('%', :texto, '%')) ORDER BY c.anioCpto DESC, c.cpto")
    List<TFConcept> findByDescripcionContaining(@Param("texto") String texto);

    /**
     * Buscar conceptos por código específico
     */
    List<TFConcept> findByCptoOrderByAnioCptoDesc(Integer cpto);

    /**
     * Obtener todos los tipos de concepto únicos
     */
    @Query("SELECT DISTINCT c.tipoCpto FROM TFConcept c WHERE c.tipoCpto IS NOT NULL ORDER BY c.tipoCpto")
    List<String> findDistinctTiposCpto();

    /**
     * Obtener todos los años disponibles
     */
    @Query("SELECT DISTINCT c.anioCpto FROM TFConcept c WHERE c.anioCpto IS NOT NULL ORDER BY c.anioCpto DESC")
    List<Integer> findDistinctAnios();

    /**
     * Contar conceptos por tipo y año
     */
    @Query("SELECT c.tipoCpto, COUNT(c) FROM TFConcept c WHERE c.anioCpto = :anio GROUP BY c.tipoCpto ORDER BY c.tipoCpto")
    List<Object[]> countByTipoAndAnio(@Param("anio") Integer anio);

    /**
     * Buscar conceptos de retenciones (RET) del año actual
     */
    @Query("SELECT c FROM TFConcept c WHERE c.tipoCpto = 'RET' AND c.anioCpto = :anio ORDER BY c.cpto")
    List<TFConcept> findRetencionesByAnio(@Param("anio") Integer anio);

    /**
     * Buscar conceptos de deducciones (DED) del año actual
     */
    @Query("SELECT c FROM TFConcept c WHERE c.tipoCpto = 'DED' AND c.anioCpto = :anio ORDER BY c.cpto")
    List<TFConcept> findDeduccionesByAnio(@Param("anio") Integer anio);

    /**
     * Buscar aportes patronales (APP) del año actual
     */
    @Query("SELECT c FROM TFConcept c WHERE c.tipoCpto = 'APP' AND c.anioCpto = :anio ORDER BY c.cpto")
    List<TFConcept> findAportesPatronalesByAnio(@Param("anio") Integer anio);

    /**
     * Buscar conceptos por rango de códigos
     */
    @Query("SELECT c FROM TFConcept c WHERE c.anioCpto = :anio AND c.cpto BETWEEN :cptoMin AND :cptoMax ORDER BY c.cpto")
    List<TFConcept> findByRangoCodigos(@Param("anio") Integer anio, @Param("cptoMin") Integer cptoMin, @Param("cptoMax") Integer cptoMax);

    List<TFConcept> findByExternalId(@NotNull Long asientoExportId);
}
