package ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle;

import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_PROCESOS_INSTANCIAS", schema = "LAPAMPA")
@SequenceGenerator(name = "LIQ_PRC_INS_SEQ", sequenceName = "LAPAMPA.LIQ_PRC_INS_SEQ",
    allocationSize = 1)
public class LiqProcesoInstancia implements Serializable {

  private static final long serialVersionUID = -2886941591902189302L;

  @Id
  @Basic(optional = false)
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_PRC_INS_SEQ")
  private Long id;

  @Column(name = "INSTANCIA")
  private Long instancia;

  @Column(name = "PRC_ID")
  private Long prcId;

  @Column(name = "FECHA_INICIO")
  private Date fechaInicio;

  @Column(name = "FECHA_FIN")
  private Date fechaFinal;

  @Column(name = "ESTADO")
  private String estado;

}
