package ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle;

import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DEPOSITO_BLP_ANSES_DET", schema = "LAPAMPA")
@SequenceGenerator(name = "LIQ_DEP_BLP_ANSES_DET_SEQ",
    sequenceName = "LAPAMPA.LIQ_DEP_BLP_ANSES_DET_SEQ", allocationSize = 1)
public class LiqDepositoBlpAnsesDet {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_DEP_BLP_ANSES_DET_SEQ")
  private Long id;

  @Column(name = "PROC_ID")
  private Long procesoId;

  @Column(name = "REG")
  private String reg;

  @Column(name = "SUC")
  private Integer suc;

  @Column(name = "APE_NOM")
  private String apeNom;

  @Column(name = "TIPO_DOC")
  private Integer tipoDoc;

  @Column(name = "NRO_DOC")
  private Long nroDoc;

  @Column(name = "NRO_BENEF")
  private Long nroBenef;

  @Column(name = "TIPO_CUENTA")
  private Integer tipoCuenta;

  @Column(name = "NRO_CUENTA")
  private BigInteger nroCuenta; // BigInteger to store 20 digits

  @Column(name = "HABERES")
  private BigDecimal haberes;

  @Column(name = "DESCUENTOS")
  private BigDecimal opera;

  @Column(name = "LIQUIDO")
  private BigDecimal liquido;

  @Column(name = "SUC_ANSES")
  private Long sucAnses;

  @Column(name = "CUIL")
  private Long cuil;

  @Column(name = "CUIL_APO")
  private Long cuilApo;

}
