package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUETFDESCUEN", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUETFDESCUEN_SEQ", sequenceName = "LAPAMPA.TEMP_SUETFDESCUEN_SEQ",
    allocationSize = 1)
public class Suetfdescuen {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUETFDESCUEN_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "ANIOLIQ")
  private Long anioLiq;

  @Column(name = "CODENTE")
  private Long codente;

  @Column(name = "CODORI")
  private Long codori;

  @Column(name = "CONVE")
  private Long conve;

  @Column(name = "CONVENIO")
  private Long convenio;

  @Column(name = "CPTO")
  private Long cpto;

  @Column(name = "CUENTA")
  private Long cuenta;

  @Column(name = "ENTRADA")
  private Long entrada;

  @Column(name = "ENVIO")
  private Long envio;

  @Column(name = "FECHAORI")
  private Date fechaOri;

  @Column(name = "HAB")
  private String hab;

  @Column(name = "IMPORTE")
  private BigDecimal importe;

  @Column(name = "JUR")
  private String jur;

  @Column(name = "MESLIQ")
  private Long mesLiq;

  @Column(name = "MONEDA")
  private Long moneda;

  @Column(name = "NROLIQ")
  private String nroLiq;

  @Column(name = "TIPOMOV")
  private Long tipoMov;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
