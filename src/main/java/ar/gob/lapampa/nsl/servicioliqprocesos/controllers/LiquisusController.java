package ar.gob.lapampa.nsl.servicioliqprocesos.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.SueliquisusService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/liquisus")
public class LiquisusController {

  @Autowired
  private SueliquisusService sueliquisusService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','LIQSUS_R','','IS')")
  public GenericResponseDTO listarLiquisus(@RequestBody ProcesoResquestDTO procesoRequest) {
    return sueliquisusService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','LIQSUS_G','','IS')")
  public GenericResponseDTO generarLiquisus(@RequestBody GenerarLiquisusRequestDTO request) {
    return sueliquisusService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','LIQSUS_R,LIQSUS_D','','IS')")
  public GenericResponseDTO borrarLiquisus(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueliquisusService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','LIQSUS_EX','','IS')")
  public GenericResponseDTO exportarLiquisus(
      @RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueliquisusService.exportar(procesoRequest);
  }

}
