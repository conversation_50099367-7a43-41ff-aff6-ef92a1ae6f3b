package ar.gob.lapampa.nsl.servicioliqprocesos.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Sueordpagd;

@Repository
public interface SueordpagdRepository extends JpaRepository<Sueordpagd, Long> {
  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_ORDPAG_GENERAR")
  String obtenerOrdpagd(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId, @Param("P_EMPRESA_ID") Long empresaId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_ORDPAG_EXPORTAR")
  String exportarOrdpagd(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_ORDPAG_ELIMINAR")
  String borrarOrdpagd(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  List<Sueordpagd> findByAsientoExportId(Long asientoExportId);


}

