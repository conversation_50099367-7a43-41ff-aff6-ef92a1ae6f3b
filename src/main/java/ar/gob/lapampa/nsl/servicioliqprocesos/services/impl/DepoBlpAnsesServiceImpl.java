package ar.gob.lapampa.nsl.servicioliqprocesos.services.impl;

import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.TimeZone;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.BlpAnsesResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarBlpAnsesRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.LiqDepositoBlpAnses;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.LiqDepositoBlpAnsesDet;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.DepoBlpAnsesDetRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.DepoBlpAnsesRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.DepoBlpAnsesService;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DepoBlpAnsesServiceImpl implements DepoBlpAnsesService {

  private DepoBlpAnsesRepository depoBlpAnsesRepository;
  private DepoBlpAnsesDetRepository depoBlpAnsesDetRepository;

  public DepoBlpAnsesServiceImpl(DepoBlpAnsesRepository depoBlpAnsesRepository,
      DepoBlpAnsesDetRepository depoBlpAnsesDetRepository) {
    this.depoBlpAnsesRepository = depoBlpAnsesRepository;
    this.depoBlpAnsesDetRepository = depoBlpAnsesDetRepository;
  }

  @Override
  public GenericResponseDTO listar(BlpAnsesResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<LiqDepositoBlpAnses> result =
        depoBlpAnsesRepository.findByProcesoId(request.getProcesoId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

  @Override
  public GenericResponseDTO generar(GenerarBlpAnsesRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    Long procesoId = request.getProcesoId();

    String fechaHasta = request.getFechaHasta();
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
    formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
    List<LiqDepositoBlpAnses> result;
    try {
      java.util.Date fecha = formatter.parse(fechaHasta);
      formatter = new SimpleDateFormat("yyyy-MM-dd");
      Date fechaSQL = Date.valueOf(formatter.format(fecha));
      log.info("Fecha Hasta: {}", fecha);
      depoBlpAnsesRepository.obtenerBlpAnses(procesoId, fechaSQL);
      result = depoBlpAnsesRepository.findByProcesoId(procesoId);
      if (result == null) {
        response.setEstadoExito(List.of());
        response.setMensaje("No Obtenido");
      } else {
        response.setEstadoExito(result);
      }
    } catch (ParseException e) {
      e.printStackTrace();
    }
    return response;
  }

  @Override
  public GenericResponseDTO listarDetalles(BlpAnsesResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<LiqDepositoBlpAnsesDet> result =
        depoBlpAnsesDetRepository.findByProcesoId(request.getProcesoId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

}
