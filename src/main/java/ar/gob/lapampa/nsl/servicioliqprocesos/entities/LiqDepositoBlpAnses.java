package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DEPOSITO_BLP_ANSES", schema = "LAPAMPA")
@SequenceGenerator(name = "LIQ_DEP_BLP_ANSES_SEQ", sequenceName = "LAPAMPA.LIQ_DEP_BLP_ANSES_SEQ",
    allocationSize = 1)
public class LiqDepositoBlpAnses {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_DEP_BLP_ANSES_SEQ")
  private Long id;

  @Column(name = "PROC_ID")
  private Long procesoId;

  @Column(name = "PRINCIPAL")
  private String principal;

  @Column(name = "CANT_REG")
  private Long cantReg;

  @Column(name = "MONTO_TOTAL")
  private BigDecimal montoTotal;

  @Column(name = "FECHA_DESDE")
  private Date fechaDesde;

  @Column(name = "FECHA_HASTA")
  private Date fechaHasta;

}
