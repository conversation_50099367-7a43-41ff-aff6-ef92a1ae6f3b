package ar.gob.lapampa.nsl.servicioliqprocesos.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Map;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
public class DataSourceConfig {

    private static final Logger log = LoggerFactory.getLogger(DataSourceConfig.class);
    private final Environment environment;

    // Oracle Configuration Properties
    @Value("${spring.datasource.oracle.url}")
    private String oracleUrl;

    @Value("${spring.datasource.oracle.username}")
    private String oracleUsername;

    @Value("${spring.datasource.oracle.password}")
    private String oraclePassword;

    @Value("${spring.datasource.oracle.driver-class-name}")
    private String oracleDriverClassName;

    // Progress Configuration Properties
    @Value("${spring.datasource.progress.jdbc-url:${spring.datasource.progress.url}}")
    private String progressUrl;

    @Value("${spring.datasource.progress.username}")
    private String progressUsername;

    @Value("${spring.datasource.progress.password}")
    private String progressPassword;

    @Value("${spring.datasource.progress.driver-class-name}")
    private String progressDriverClassName;

    public DataSourceConfig(Environment environment) {
        this.environment = environment;
    }

    /**
     * Oracle DataSource - Primary
     * Configuración programática que respeta los perfiles activos
     */
    @Primary
    @Bean(name = "oracleDataSource")
    public DataSource oracleDataSource() {
        HikariConfig config = new HikariConfig();

        // Configuración básica Oracle desde properties
        config.setJdbcUrl(oracleUrl);
        config.setUsername(oracleUsername);
        config.setPassword(oraclePassword);
        config.setDriverClassName(oracleDriverClassName);

        // Configuración del pool Oracle optimizada por perfil
        String activeProfile = getActiveProfile();
        configureOraclePool(config, activeProfile);

        return new HikariDataSource(config);
    }

    /**
     * Progress DataSource - Secondary
     * Configuración programática que respeta los perfiles activos
     */
    @Bean(name = "progressDataSource")
    public DataSource progressDataSource() {
        try {
            log.info("Intentando crear DataSource para Progress OpenEdge...");

            HikariConfig config = new HikariConfig();

            // Configuración básica Progress desde properties
            config.setJdbcUrl(progressUrl);
            config.setUsername(progressUsername);
            config.setPassword(progressPassword);
            config.setDriverClassName(progressDriverClassName);

            // Configuración del pool Progress optimizada por perfil
            String activeProfile = getActiveProfile();
            configureProgressPool(config, activeProfile);

            // Configuración adicional para manejo de errores
            config.setConnectionTimeout(10000); // 10 segundos timeout
            config.setValidationTimeout(5000);  // 5 segundos validación
            config.setInitializationFailTimeout(-1); // No fallar en inicialización

            HikariDataSource dataSource = new HikariDataSource(config);
            log.info("✅ DataSource Progress creado exitosamente");
            return dataSource;

        } catch (Exception e) {
            log.error("❌ Error al crear DataSource Progress: {}", e.getMessage());
            log.warn("🔄 Creando DataSource Progress en modo fallback (sin conexión)");

            // Crear un DataSource que no falle pero que no funcione
            return createFallbackProgressDataSource();
        }
    }

    /**
     * Crea un DataSource de fallback para Progress que no falla al inicializar
     */
    private DataSource createFallbackProgressDataSource() {
        HikariConfig config = new HikariConfig();

        // Configuración mínima que no requiere conexión inmediata
        config.setJdbcUrl(progressUrl);
        config.setUsername(progressUsername);
        config.setPassword(progressPassword);
        config.setDriverClassName(progressDriverClassName);

        // Configuración para evitar fallos en startup
        config.setMinimumIdle(0);           // Sin conexiones mínimas
        config.setMaximumPoolSize(1);       // Pool mínimo
        config.setConnectionTimeout(5000);  // Timeout corto
        config.setValidationTimeout(3000);  // Validación corta
        config.setInitializationFailTimeout(-1); // No fallar en inicialización
        config.setConnectionTestQuery("SELECT 1"); // Query simple de test

        // Pool name específico para fallback
        String activeProfile = getActiveProfile();
        config.setPoolName("HikariPool-progress-fallback-" + activeProfile);

        try {
            return new HikariDataSource(config);
        } catch (Exception e) {
            log.error("❌ Error crítico creando DataSource Progress fallback: {}", e.getMessage());
            throw new RuntimeException("No se puede crear DataSource Progress ni en modo fallback", e);
        }
    }

    /**
     * Obtiene el perfil activo principal
     */
    private String getActiveProfile() {
        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length > 0) {
            // Buscar el perfil principal (no datasource ni progress)
            for (String profile : activeProfiles) {
                if (!profile.equals("datasource") && !profile.equals("progress")) {
                    return profile;
                }
            }
            return activeProfiles[0];
        }
        return "desa"; // Default profile
    }

    /**
     * Configura el pool de Oracle según el perfil activo
     */
    private void configureOraclePool(HikariConfig config, String profile) {
        // Configuración base común
        config.setConnectionTimeout(20000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1200000);
        config.setLeakDetectionThreshold(90000);
        config.setConnectionTestQuery("SELECT 1 FROM DUAL");

        // Configuración específica por perfil
        switch (profile.toLowerCase()) {
            case "local":
                config.setMinimumIdle(2);
                config.setMaximumPoolSize(5);
                config.setPoolName("HikariPool-oracle-local");
                break;
            case "desa":
                config.setMinimumIdle(3);
                config.setMaximumPoolSize(8);
                config.setPoolName("HikariPool-oracle-desa");
                break;
            case "test":
                config.setMinimumIdle(3);
                config.setMaximumPoolSize(10);
                config.setPoolName("HikariPool-oracle-test");
                break;
            case "prod":
                config.setMinimumIdle(5);
                config.setMaximumPoolSize(15);
                config.setPoolName("HikariPool-oracle-prod");
                // Configuración más estricta para producción
                config.setConnectionTimeout(30000);
                config.setLeakDetectionThreshold(60000);
                break;
            default:
                config.setMinimumIdle(3);
                config.setMaximumPoolSize(8);
                config.setPoolName("HikariPool-oracle-default");
        }
    }

    /**
     * Configura el pool de Progress según el perfil activo
     */
    private void configureProgressPool(HikariConfig config, String profile) {
        // Configuración base común
        config.setConnectionTimeout(20000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1200000);
        config.setLeakDetectionThreshold(90000);
        config.setConnectionTestQuery("SELECT 1 FROM sysprogress.syscalctable");

        // Configuración específica por perfil
        switch (profile.toLowerCase()) {
            case "local":
                config.setMinimumIdle(1);
                config.setMaximumPoolSize(3);
                config.setPoolName("HikariPool-progress-local");
                break;
            case "desa":
                config.setMinimumIdle(2);
                config.setMaximumPoolSize(5);
                config.setPoolName("HikariPool-progress-desa");
                break;
            case "test":
                config.setMinimumIdle(2);
                config.setMaximumPoolSize(6);
                config.setPoolName("HikariPool-progress-test");
                break;
            case "prod":
                config.setMinimumIdle(3);
                config.setMaximumPoolSize(10);
                config.setPoolName("HikariPool-progress-prod");
                // Configuración más estricta para producción
                config.setConnectionTimeout(30000);
                config.setLeakDetectionThreshold(60000);
                break;
            default:
                config.setMinimumIdle(2);
                config.setMaximumPoolSize(5);
                config.setPoolName("HikariPool-progress-default");
        }
    }

    /**
     * Oracle EntityManagerFactory
     */
    @Primary
    @Bean(name = "oracleEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean oracleEntityManagerFactory(
            @Qualifier("oracleDataSource") DataSource dataSource) {

        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);

        // Configuración específica para Oracle
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.OracleDialect");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.jdbc.batch_size", "20");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.temp.use_jdbc_metadata_defaults", "false");
        properties.put("hibernate.connection.provider_disables_autocommit", "false");
        em.setJpaPropertyMap(properties);

        return em;
    }

    /**
     * Progress EntityManagerFactory
     */
    @Bean(name = "progressEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean progressEntityManagerFactory(
            @Qualifier("progressDataSource") DataSource dataSource) {

        try {
            log.info("Creando EntityManagerFactory para Progress...");

            LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
            em.setDataSource(dataSource);
            em.setPackagesToScan("ar.gob.lapampa.nsl.servicioliqprocesos.entities.progress");

            HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
            em.setJpaVendorAdapter(vendorAdapter);

            // Configuración específica para Progress
            Map<String, Object> properties = new HashMap<>();
            // Usar dialecto genérico compatible con Progress
            // Progress OpenEdge es similar a PostgreSQL en muchos aspectos
            // Opción 1: PostgreSQL (recomendado para empezar)
            properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
            // Opción 2: Dialecto personalizado (descomentar si es necesario)
            // properties.put("hibernate.dialect", "ar.gob.lapampa.nsl.admin.config.ProgressDialectSimple");
            properties.put("hibernate.hbm2ddl.auto", "none");
            properties.put("hibernate.show_sql", "false");
            properties.put("hibernate.format_sql", "true");
            properties.put("hibernate.jdbc.batch_size", "20");
            properties.put("hibernate.order_inserts", "true");
            properties.put("hibernate.order_updates", "true");
            // Configuraciones específicas para Progress
            properties.put("hibernate.connection.autocommit", "false");
            properties.put("hibernate.jdbc.use_scrollable_resultset", "false");
            properties.put("hibernate.temp.use_jdbc_metadata_defaults", "false");
            properties.put("hibernate.connection.provider_disables_autocommit", "false");

            // Configuraciones adicionales para manejo de errores
            properties.put("hibernate.jdbc.lob.non_contextual_creation", "true");

            em.setJpaPropertyMap(properties);

            log.info("✅ EntityManagerFactory Progress creado exitosamente");
            return em;

        } catch (Exception e) {
            log.error("❌ Error al crear EntityManagerFactory Progress: {}", e.getMessage());
            log.warn("🔄 Progress EntityManagerFactory en modo degradado");

            // Retornar un EntityManagerFactory básico que no falle
            LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
            em.setDataSource(dataSource);
            em.setPackagesToScan("ar.gob.lapampa.nsl.servicioliqprocesos.entities.progress");

            HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
            em.setJpaVendorAdapter(vendorAdapter);

            // Configuración mínima
            Map<String, Object> properties = new HashMap<>();
            properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
            properties.put("hibernate.hbm2ddl.auto", "none");
            properties.put("hibernate.show_sql", "false");
            em.setJpaPropertyMap(properties);

            return em;
        }
    }

    /**
     * Oracle Transaction Manager
     */
    @Primary
    @Bean(name = "oracleTransactionManager")
    public PlatformTransactionManager oracleTransactionManager(
            @Qualifier("oracleEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }

    /**
     * Progress Transaction Manager
     */
    @Bean(name = "progressTransactionManager")
    public PlatformTransactionManager progressTransactionManager(
            @Qualifier("progressEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
