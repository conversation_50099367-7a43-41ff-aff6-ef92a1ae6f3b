package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUETFDETALLE", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUETFDETALLE_SEQ", sequenceName = "LAPAMPA.TEMP_SUETFDETALLE_SEQ",
    allocationSize = 1)
public class Suetfdetalle {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUETFDETALLE_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "ANIOLIQ")
  private Long anioLiq;

  @Column(name = "BCOPAGO")
  private Long bcoPago;

  @Column(name = "CARATU")
  private String caratu;

  @Column(name = "CBU1")
  private BigDecimal cbu1;

  @Column(name = "CBU2")
  private String cbu2;

  @Column(name = "CIRC")
  private Long circ;

  @Column(name = "CLIENTE")
  private Long cliente;

  @Column(name = "CODDOC")
  private Long coddoc;

  @Column(name = "CODENTE")
  private Long codente;

  @Column(name = "CODORI")
  private Long codori;

  @Column(name = "CODPROV")
  private String codProv;

  @Column(name = "CONVE")
  private Long conve;

  @Column(name = "CONVENIO")
  private Long convenio;

  @Column(name = "CPTO")
  private Long cpto;

  @Column(name = "CTADES")
  private Long ctades;

  @Column(name = "CTAORI")
  private Long ctaOri;

  @Column(name = "CUENTA")
  private Long cuenta;

  @Column(name = "CUITPROV")
  private Long cuitProv;

  @Column(name = "ENTRADA")
  private Long entrada;

  @Column(name = "ENVIO")
  private Long envio;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "EXPTE")
  private String expte;

  @Column(name = "FECHAENV")
  private Date fechaEnv;

  @Column(name = "FECHAORI")
  private Date fechaOri;

  @Column(name = "FECHAPAG")
  private Date fechaPag;

  @Column(name = "FORMAPAG")
  private Long formaPag;

  @Column(name = "HAB")
  private String hab;

  @Column(name = "IMPORTE")
  private BigDecimal importe;

  @Column(name = "JUR")
  private String jur;

  @Column(name = "JUZGA")
  private String juzga;

  @Column(name = "MARCALIS")
  private String marcalis;

  @Column(name = "MESLIQ")
  private Long mesLiq;

  @Column(name = "MONEDA")
  private Long moneda;

  @Column(name = "NOMPROV")
  private String nomProv;

  @Column(name = "NOMSUC")
  private String nomSuc;

  @Column(name = "NORDEN")
  private String nOrden;

  @Column(name = "NRODOC")
  private Long nroDoc;

  @Column(name = "NROLIQ")
  private String nroLiq;

  @Column(name = "OBSER")
  private String obser;

  @Column(name = "OFICIO")
  private String oficio;

  @Column(name = "PERIODO")
  private Long periodo;

  @Column(name = "SECRE")
  private String secre;

  @Column(name = "SISDES")
  private Long sisdes;

  @Column(name = "SISORI")
  private Long sisOri;

  @Column(name = "SUCDES")
  private Long sucdes;

  @Column(name = "SUCORI")
  private Long sucOri;

  @Column(name = "SUCPAGO")
  private Long sucPago;

  @Column(name = "SUCURSAL")
  private Long sucursal;

  @Column(name = "TIPODES")
  private Long tipodes;

  @Column(name = "TIPOMOV")
  private Long tipoMov;

  @Column(name = "TIPORI")
  private Long tipOri;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
