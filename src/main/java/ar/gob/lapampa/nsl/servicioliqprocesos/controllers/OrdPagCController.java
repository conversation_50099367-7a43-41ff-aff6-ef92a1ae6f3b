package ar.gob.lapampa.nsl.servicioliqprocesos.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarOrdPagRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.SueOrdPagCService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/ordpagc")
public class OrdPagCController {

  @Autowired
  private SueOrdPagCService sueOrdPagCService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','OPAGOC_R','','IS')")
  public GenericResponseDTO listarOrdPagC(@RequestBody ProcesoResquestDTO procesoRequest) {
    return sueOrdPagCService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','OPAGOC_G','','IS')")
  public GenericResponseDTO generarOrdPagC(@RequestBody GenerarOrdPagRequestDTO request) {
    return sueOrdPagCService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','OPAGOC_R,OPAGOC_D','','IS')")
  public GenericResponseDTO borrarOrdPagC(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueOrdPagCService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','OPAGOC_EX','','IS')")
  public GenericResponseDTO exportarOrdPagC(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueOrdPagCService.exportar(procesoRequest);
  }


}
