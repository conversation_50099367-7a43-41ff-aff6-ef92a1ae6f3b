package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.progress;

import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarSuetfconceptRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.progress.PTFConceptService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/progress/tfconcept")
public class PTfConceptController {

  @Autowired
  private PTFConceptService pTFConceptService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','TFCON_R','','IS')")
  public GenericResponseDTO listarTfConcept(@RequestBody ProcesoResquestDTO procesoRequest) {
    return pTFConceptService.listar(procesoRequest);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','TFCON_R, TFCON_D','','IS')")
  public GenericResponseDTO borrarTfConcept(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return pTFConceptService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','TFCON_EX','','IS')")
  public GenericResponseDTO exportarTfConcept(
      @RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return pTFConceptService.exportar(procesoRequest);
  }

}
