package ar.gob.lapampa.nsl.servicioliqprocesos.entities.progress;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entidad para la tabla SUETFCONCEPT de Progress - Conceptos de Sueldo
 */
@Entity
@Table(name = "SUETFCONCEPT", schema = "PUB")
@IdClass(ConceptoSueldo.ConceptoSueldoId.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConceptoSueldo {

    @Id
    @Column(name = "AnioCpto")
    private Integer anioCpto;

    @Id
    @Column(name = "Cpto")
    private Integer cpto;

    @Column(name = "Descrip", length = 56)
    private String descrip;

    @Column(name = "MesCpto")
    private Integer mesCpto;

    @Column(name = "TipoCpto", length = 7)
    private String tipoCpto;

    @Column(name = "MarcaCopiado", length = 12)
    private String marcaCopiado;

    @Column(name = "external_id")
    private Integer externalId;

    @Column(name = "instancia_id")
    private Integer instanciaId;

    /**
     * Clase para la clave compuesta
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConceptoSueldoId implements Serializable {
        private Integer anioCpto;
        private Integer cpto;
    }

    /**
     * Método de conveniencia para obtener la descripción del tipo de concepto
     */
    public String getTipoConceptoDescripcion() {
        if (tipoCpto == null) return "Sin tipo";
        
        return switch (tipoCpto.trim()) {
            case "ANT" -> "Anticipo";
            case "RET" -> "Retención";
            case "DED" -> "Deducción";
            case "APP" -> "Aporte Patronal";
            case "HAB" -> "Haberes";
            case "BON" -> "Bonificación";
            default -> tipoCpto;
        };
    }

    /**
     * Método de conveniencia para verificar si es un concepto activo
     */
    public boolean isConceptoActivo() {
        return anioCpto != null && anioCpto >= 2024;
    }

    /**
     * Método de conveniencia para obtener el código completo
     */
    public String getCodigoCompleto() {
        return String.format("%d-%03d", anioCpto != null ? anioCpto : 0, cpto != null ? cpto : 0);
    }
}
