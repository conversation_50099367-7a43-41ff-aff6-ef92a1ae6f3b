package ar.gob.lapampa.nsl.servicioliqprocesos;

import java.time.Duration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

@SpringBootApplication
@EnableAutoConfiguration
public class ServicioLiqProcesosApplication {

  public static void main(String[] args) {
    SpringApplication.run(ServicioLiqProcesosApplication.class, args);
  }

  /**
   * Open API Definitions.
   *
   * @param properties
   * @return open API resume
   */
  @Bean
  OpenAPI customOpenAPI(@Value("${project.description}") String appDesciption,
      @Value("${project.version}") String appVersion) {
    return new OpenAPI().info(new Info().title("Servidor API para Gestionar Informes")
        .version(appVersion).description(appDesciption).termsOfService("http://swagger.io/terms/")
        .license(new License().name("Apache 2.0").url("http://springdoc.org")));
  }

  /**
   * Rest template rest template.
   *
   * @param builder the builder
   * @return the rest template
   */
  @Bean
  @LoadBalanced
  RestTemplate restTemplate(RestTemplateBuilder builder) {
    return builder.readTimeout(Duration.ofSeconds(25)).connectTimeout(Duration.ofSeconds(25))
        .build();
  }

}
