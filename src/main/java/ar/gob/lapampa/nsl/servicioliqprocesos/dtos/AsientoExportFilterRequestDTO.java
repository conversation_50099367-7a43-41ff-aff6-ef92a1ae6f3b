package ar.gob.lapampa.nsl.servicioliqprocesos.dtos;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AsientoExportFilterRequestDTO implements Serializable {

  private static final long serialVersionUID = 7594121236663028122L;

  @Nonnull
  private String userLogin;

  private Integer anio;

  private Integer mes;

  private Long procesoId;

  private String nombre;

  private Long empresaId;

  private String estado;

}
