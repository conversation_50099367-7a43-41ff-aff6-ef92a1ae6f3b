package ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle;

import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUEPEMPRETC", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUEPEMPRETC_SEQ", sequenceName = "LAPAMPA.TEMP_SUEPEMPRETC_SEQ",
    allocationSize = 1)
public class Suepempretc {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUEPEMPRETC_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "ANIOL")
  private Long aniol;

  @Column(name = "CARATU")
  private String caratu;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "FECHA")
  private Date fecha;

  @Column(name = "HORA")
  private Long hora;

  @Column(name = "MESL")
  private Long mesl;

  @Column(name = "NMBR")
  private String nmbr;

  @Column(name = "OPERA")
  private String opera;

  @Column(name = "PASADO")
  private String pasado;

  @Column(name = "PCCANR")
  private Long pccanr;

  @Column(name = "PCCARC")
  private Long pccarc;

  @Column(name = "PCFECF")
  private Long pcfecf;

  @Column(name = "PCFECI")
  private Long pcfeci;

  @Column(name = "PCID")
  private Long pcid;

  @Column(name = "PCMONT")
  private Long pcmont;

  @Column(name = "PCMOTC")
  private Long pcmotc;

  @Column(name = "PCPERI")
  private Long pcperi;

  @Column(name = "TLIQ")
  private String tliq;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
