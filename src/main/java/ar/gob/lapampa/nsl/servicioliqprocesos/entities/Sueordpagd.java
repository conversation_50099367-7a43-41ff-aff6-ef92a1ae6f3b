package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUEORDPAGD", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUEORDPAGD_SEQ", sequenceName = "LAPAMPA.TEMP_SUEORDPAGD_SEQ",
    allocationSize = 1)
public class Sueordpagd {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUEORDPAGD_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "CODHAB")
  private String codHab;

  @Column(name = "CUENTA")
  private Long cuenta;

  @Column(name = "IMPBRUTO")
  private BigDecimal impBruto;

  @Column(name = "IMPCRED")
  private BigDecimal impCred;

  @Column(name = "IMPCREDL")
  private BigDecimal impCredL;

  @Column(name = "IMPNETO")
  private BigDecimal impNeto;

  @Column(name = "IMPNETOL")
  private BigDecimal impNetoL;

  @Column(name = "JUR")
  private String jur;

  @Column(name = "LIQANIO")
  private Long liqanio;

  @Column(name = "LIQMES")
  private Long liqmes;

  @Column(name = "LIQTIPO")
  private String liqTipo;

  @Column(name = "MARCALIS")
  private Long marcaLis;

  @Column(name = "NROLIQ")
  private String nroLiq;

  @Column(name = "NROPED")
  private Long nroPed;

  @Column(name = "SEC")
  private Long sec;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
