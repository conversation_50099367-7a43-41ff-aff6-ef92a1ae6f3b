package ar.gob.lapampa.nsl.servicioliqprocesos.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EnableJpaRepositories(
    basePackages = "ar.gob.lapampa.nsl.servicioliqprocesos.repositories.progress",
    entityManagerFactoryRef = "progressEntityManagerFactory",
    transactionManagerRef = "progressTransactionManager"
)
public class ProgressDataSourceConfig {
    // Esta clase solo define la configuración de repositorios para Progress
    // Los beans están definidos en DataSourceConfig
}
