package ar.gob.lapampa.nsl.servicioliqprocesos.services.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.JsonNode;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoDescriptorDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportFilterRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportGenerarRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.HeaderParameterDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcedureParameterDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.AsientoExport;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.LiqProcesoInstancia;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.RrhhEmpresa;
import ar.gob.lapampa.nsl.servicioliqprocesos.mappers.AsientoExportMapper;
import ar.gob.lapampa.nsl.servicioliqprocesos.mappers.RrrhhEmpresaMapper;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.AsientoExportRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.AsientoExportRepository.EmpresaDescriptor;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.AsientoExportRepository.ProcesoDescriptor;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.ProcesoInstanciaRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.RrhhEmpresaRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.AsientoExportService;
import ar.gob.lapampa.nsl.servicioliqprocesos.utils.Constants;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AsientoExportServiceImpl implements AsientoExportService {

  private AsientoExportRepository asientoExportRepository;
  private RrhhEmpresaRepository rrhhEmpresaRepository;
  private ProcesoInstanciaRepository procesoInstanciaRepository;

  // Pedido de fondos
  private static final String ORDPAGC_TXT = "ordpagc";
  private static final String ORDPAGD_TXT = "ordpagd";

  public AsientoExportServiceImpl(AsientoExportRepository asientoExportRepository,
      RrhhEmpresaRepository rrhhEmpresaRepository,
      ProcesoInstanciaRepository procesoInstanciaRepository) {
    super();
    this.asientoExportRepository = asientoExportRepository;
    this.rrhhEmpresaRepository = rrhhEmpresaRepository;
    this.procesoInstanciaRepository = procesoInstanciaRepository;
  }

  private String userLog = "AnonymousUser";
  private Long procesoId = 0L;

  @Override
  public GenericResponseDTO listaProcesos(AsientoExportRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String currentPrincipalName = "";
    List<AsientoExportResponseDTO> resp;
    if (authentication != null) {
      currentPrincipalName = authentication.getName();
      if (Objects.isNull(request.getProcesoId())) {
        resp = AsientoExportMapper.toAsientoExportResponseDTOList(
            asientoExportRepository.findByUserLogAndParent(currentPrincipalName, null));
      } else {
        resp = AsientoExportMapper.toAsientoExportResponseDTOList(
            asientoExportRepository.findByProcesoIdAndUserLogAndParent(request.getProcesoId(),
                currentPrincipalName, null)); // parent=null lista solo asientos padres
      }
    } else {
      response.setEstadoError("User Login not found");
      response.setMensaje("User Login ausente");
      return response;
    }
    if (!resp.isEmpty()) {
      if (request.getProcesoId() == null) {
        resp = AsientoExportMapper.toAsientoExportResponseDTOList(
            asientoExportRepository.findByUserLog(currentPrincipalName));
      } else {
        if (request.getEstado() == null) {
          resp = AsientoExportMapper.toAsientoExportResponseDTOList(asientoExportRepository
              .findByProcesoIdAndUserLog(request.getProcesoId(), currentPrincipalName));
        } else {
          resp = AsientoExportMapper.toAsientoExportResponseDTOList(
              asientoExportRepository.findByProcesoIdAndEstadoAndUserLog(request.getProcesoId(),
                  request.getEstado(), currentPrincipalName));
        }
      }
      response.setEstadoExito(setAnioAndMesBulk(resp));
      response.setMensaje("Registros: " + resp.size());
    } else {
      response.setEstadoExito(resp);
      response.setMensaje("Registros: 0");
    }
    return response;
  }



  @Override
  public GenericResponseDTO generarProcesos(AsientoExportGenerarRequestDTO request) {

    GenericResponseDTO response = new GenericResponseDTO();
    this.procesoId = request.getProcesoId();

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    List<AsientoExportResponseDTO> resp;
    if (Objects.nonNull(authentication)) {
      this.userLog = authentication.getName();
      // Si user autenticado es distinto al del request prioriza request
      if (!request.getUserLogin().equals(this.userLog)) {
        this.userLog = request.getUserLogin();
      }
    } else {
      response.setEstadoError("User Login not found");
      response.setMensaje("User Login ausente");
      return response;
    }

    AsientoDescriptorDTO asientoEncolado = generarPorNombre(request, request.getNombre());
    if (Objects.nonNull(asientoEncolado) && asientoEncolado.getAsociado() != null) {
      encolarAsociados(asientoEncolado, request);
    }

    if (request.getEstado() == null) {
      resp = AsientoExportMapper.toAsientoExportResponseDTOList(
          asientoExportRepository.findByProcesoIdAndUserLog(request.getProcesoId(), this.userLog));
    } else {
      resp = AsientoExportMapper.toAsientoExportResponseDTOList(
          asientoExportRepository.findByProcesoIdAndEstadoAndUserLog(request.getProcesoId(),
              request.getEstado(), this.userLog));
    }

    if (Objects.nonNull(request.getEmpresaId())
        && (ORDPAGC_TXT.equals(request.getNombre()) || ORDPAGD_TXT.equals(request.getNombre()))) {
      resp = AsientoExportMapper.toAsientoExportResponseDTOList(
          asientoExportRepository.findAllByUserLogAndProcesoAndEmpresaAndTabla(this.userLog,
              request.getProcesoId(), request.getEmpresaId(), request.getNombre()));
      Optional<AsientoExport> optPadre = asientoExportRepository.findById(resp.get(0).getParent());
      if (optPadre.isPresent()) {
        resp.add(AsientoExportMapper.toAsientoExportResponseDTO(optPadre.get()));
      }
    }

    response.setEstadoExito(setAnioAndMesBulk(resp));
    response.setMensaje("Registros: " + resp.size());
    return response;
  }

  private void encolarAsociados(AsientoDescriptorDTO descriptor,
      AsientoExportGenerarRequestDTO request) {
    AsientoDescriptorDTO asientoAsociado = null;
    do {
      // se trata de asiento padre se crea y encola el asiento hijo
      request.setNombre(descriptor.getAsociado());
      asientoAsociado = generarPorNombre(request, descriptor.getAsociado());
      if (Objects.nonNull(asientoAsociado)) {
        Optional<AsientoExport> optAsientoHijo =
            asientoExportRepository.findById(asientoAsociado.getAsientoId());
        if (optAsientoHijo.isPresent()) {
          AsientoExport asientoHijo = optAsientoHijo.get();
          asientoHijo.setParent(descriptor.getAsientoId());
          asientoExportRepository.saveAndFlush(asientoHijo);
        }
        descriptor = asientoAsociado;
      }
    } while (Objects.nonNull(asientoAsociado) && Objects.nonNull(asientoAsociado.getAsociado()));
  }

  @Override
  public GenericResponseDTO byId(Long id) {
    GenericResponseDTO response = new GenericResponseDTO();
    Optional<AsientoExport> optionalAsiento = asientoExportRepository.findById(id);
    if (optionalAsiento.isPresent()) {
      AsientoExportResponseDTO asientoDto =
          AsientoExportMapper.toAsientoExportResponseDTO(optionalAsiento.get());
      response.setEstadoExito(setDescriptor(asientoDto));
    } else {
      response.setEstadoError("No se encontraron asientos para el id: " + id);
    }
    return response;
  }

  @Override
  public GenericResponseDTO listaProcesosFiltrado(AsientoExportFilterRequestDTO filterRequest) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<AsientoExportResponseDTO> resp;
    List<Long> listaProcesos = new ArrayList<>();

    if (Objects.isNull(filterRequest.getProcesoId())) {
      listaProcesos =
          asientoExportRepository.getProcesosId(filterRequest.getAnio(), filterRequest.getMes());
    } else {
      listaProcesos.add(filterRequest.getProcesoId());
    }

    resp = AsientoExportMapper.toAsientoExportResponseDTOList(asientoExportRepository
        .findByFilter(listaProcesos, filterRequest.getNombre(), filterRequest.getEmpresaId()));
    // se saco el filtro de usuario 16/12/2024 pali
    // asientoExportRepository.findByFilter(filterRequest.getUserLogin(),
    // listaProcesos,filterRequest.getNombre(), filterRequest.getEmpresaId()));

    response.setEstadoExito(setAnioAndMesBulk(resp));
    response.setMensaje(Integer.toString(resp.size()));
    return response;
  }

  @Override
  public GenericResponseDTO listaEmpresasPorProcesoId(Long procesoId) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<EmpresaDescriptor> listaEmpresas =
        asientoExportRepository.findAllEmpresaDescriptorByProcesoId(procesoId);
    response.setEstadoExito(listaEmpresas);
    response.setMensaje("" + listaEmpresas.size());
    return response;
  }

  private List<AsientoExportResponseDTO> setAnioAndMesBulk(
      List<AsientoExportResponseDTO> asientoDtos) {
    asientoDtos.forEach(asientoDto -> {
      this.setDescriptor(asientoDto);
      this.setInstancia(asientoDto);
    });
    return asientoDtos;
  }

  private AsientoExportResponseDTO setDescriptor(AsientoExportResponseDTO asientoDto) {
    Optional<ProcesoDescriptor> procesoDescriptor =
        asientoExportRepository.getProcesoDescriptorByProcesoId(asientoDto.getProcesoId());
    if (procesoDescriptor.isPresent()) {
      asientoDto.setAnio(procesoDescriptor.get().getAnio());
      asientoDto.setMes(procesoDescriptor.get().getMes());
      asientoDto.setTipoProceso(procesoDescriptor.get().getTipoProceso());
    }
    return asientoDto;
  }

  private AsientoExportResponseDTO setInstancia(AsientoExportResponseDTO asientoDto) {
    if (asientoDto.getInsId() != null) {
      Optional<LiqProcesoInstancia> procesoInstancia =
          procesoInstanciaRepository.findById(asientoDto.getInsId());
      if (procesoInstancia.isPresent()) {
        asientoDto.setInstancia(procesoInstancia.get().getInstancia());
      }
    }
    return asientoDto;
  }

  private List<ProcedureParameterDTO> obtieneListaParametros(JsonNode jsonRoot) {
    List<ProcedureParameterDTO> listaParams = new ArrayList<>();
    JsonNode parameters = jsonRoot.path("FieldParameters");
    if (parameters.isArray()) {
      for (JsonNode param : parameters) {
        ProcedureParameterDTO procedureParameterDTO = new ProcedureParameterDTO();
        procedureParameterDTO.setName(param.get("name").asText());
        procedureParameterDTO.setParamName(param.get("paramName").asText());
        procedureParameterDTO.setComment(param.get("comment").asText());
        procedureParameterDTO.setDefaultValue(param.get("defaultValue").asText());
        procedureParameterDTO.setValueType(param.get("valueType").asText());
        procedureParameterDTO.setEndPoint(param.get("endPoint").asText());
        procedureParameterDTO.setEndPointRequest(param.get("endPointRequest").asText());
        procedureParameterDTO.setIsNullable(param.get("isNullable").asBoolean());
        procedureParameterDTO.setIsInput(param.get("isInput").asBoolean());
        listaParams.add(procedureParameterDTO);
      }
    }
    return listaParams;
  }

  private List<HeaderParameterDTO> obtieneListaHeaders(JsonNode jsonRoot) {
    List<HeaderParameterDTO> listaHeaders = new ArrayList<>();
    JsonNode headers = jsonRoot.path("TableHeaders");
    if (headers.isArray()) {
      for (JsonNode header : headers) {
        HeaderParameterDTO headerParameterDTO = new HeaderParameterDTO();
        headerParameterDTO.setName(header.get("name").asText());
        headerParameterDTO.setParamName(header.get("paramName").asText());
        headerParameterDTO.setComment(header.get("comment").asText());
        headerParameterDTO.setOrder(header.get("order").asInt());
        headerParameterDTO.setValueType(header.get("valueType").asText());
        headerParameterDTO.setColor(header.get("color").asText());
        listaHeaders.add(headerParameterDTO);
      }
    }
    return listaHeaders;
  }

  private List<String> obtieneListaHidden(JsonNode jsonRoot) {
    List<String> listaHeaders = new ArrayList<>();
    JsonNode headers = jsonRoot.path("HiddenColumns");
    if (headers.isArray()) {
      for (JsonNode header : headers) {
        listaHeaders.add(header.textValue());
      }
    }
    return listaHeaders;
  }

  @Override
  public AsientoDescriptorDTO obtieneAsientoDeJson(JsonNode jsonRoot) {
    AsientoDescriptorDTO asientoDescriptor = new AsientoDescriptorDTO();
    JsonNode descriptor = jsonRoot.path("AsientoDescriptor");
    if (!descriptor.isNull()) {
      asientoDescriptor.setGeneralName(descriptor.get("generalName").asText());
      asientoDescriptor.setGenerarURL(descriptor.get("generarURL").asText());
      asientoDescriptor.setExportarURL(descriptor.get("exportarURL").asText());
      asientoDescriptor.setTableName(descriptor.get("tableName").asText());
      if (!descriptor.get("asociado").isNull()) {
        asientoDescriptor.setAsociado(descriptor.get("asociado").asText());
      }
      asientoDescriptor.setFieldParameters(obtieneListaParametros(jsonRoot));
      asientoDescriptor.setTableHeaders(obtieneListaHeaders(jsonRoot));
      asientoDescriptor.setHiddenColumns(obtieneListaHidden(jsonRoot));
    }
    return asientoDescriptor;
  }

  private AsientoExportResponseDTO crearAsiento(JsonNode json,
      AsientoExportGenerarRequestDTO request) {
    List<AsientoExportResponseDTO> resp;
    String tabla = request.getNombre();

    if (ORDPAGC_TXT.equals(tabla) || ORDPAGD_TXT.equals(tabla)) {
      resp = AsientoExportMapper.toAsientoExportResponseDTOList(
          asientoExportRepository.findAllByUserLogAndProcesoAndEmpresaAndTabla(this.userLog,
              request.getProcesoId(), request.getEmpresaId(), tabla));
    } else {
      resp = AsientoExportMapper.toAsientoExportResponseDTOList(
          asientoExportRepository.findByProcesoIdAndTablaAndUserLog(request.getProcesoId(), tabla,
              request.getUserLogin()));
    }

    if (resp.isEmpty()) {
      return insertarAsiento(json, request);
    } else {
      return resp.get(0);
    }

  }

  private AsientoExportResponseDTO insertarAsiento(JsonNode json,
      AsientoExportGenerarRequestDTO request) {

    AsientoExportResponseDTO asientoDto = new AsientoExportResponseDTO();
    AsientoDescriptorDTO asientoDescriptor = obtieneAsientoDeJson(json);

    JSONArray jsonArray = new JSONArray(asientoDescriptor.getFieldParameters());
    log.info("CAMPOS: {}", jsonArray.toString());
    asientoDto.setCampos(jsonArray.toString());

    jsonArray = new JSONArray(asientoDescriptor.getTableHeaders());
    log.info("CABECERAS: {}", jsonArray.toString());
    asientoDto.setCabeceras(jsonArray.toString());

    asientoDto.setProcesoId(this.procesoId);
    asientoDto.setNombre(asientoDescriptor.getGeneralName());
    asientoDto.setProcedure(asientoDescriptor.getExportarURL());
    asientoDto.setUrl(asientoDescriptor.getGenerarURL());
    asientoDto.setEstado(Constants.Status.ENCOLADO.label);
    asientoDto.setUserLog(this.userLog);
    asientoDto.setExportable("N");
    asientoDto.setTabla(asientoDescriptor.getTableName());
    asientoDto.setAsociado(asientoDescriptor.getAsociado());
    asientoDto.setInsId(request.getInsId());
    // if (Objects.nonNull(request.getFechaHasta())) {
    // asientoDto.setFechaHasta(request.getFechaHasta());
    // }

    if (Objects.nonNull(request.getEmpresaId())) {
      Optional<RrhhEmpresa> optEmpresa = rrhhEmpresaRepository.findById(request.getEmpresaId());
      if (optEmpresa.isPresent()) {
        asientoDto.setEmpresa(RrrhhEmpresaMapper.toRrhhEmpresaDTO(optEmpresa.get()));
      }

    }

    Optional<ProcesoDescriptor> procesoDescriptor =
        asientoExportRepository.getProcesoDescriptorByProcesoId(asientoDto.getProcesoId());
    if (procesoDescriptor.isPresent()) {
      asientoDto.setAnio(procesoDescriptor.get().getAnio());
      asientoDto.setMes(procesoDescriptor.get().getMes());
      asientoDto.setTipoProceso(procesoDescriptor.get().getTipoProceso());
    }

    AsientoExport asientoNuevo = AsientoExportMapper.toAsientoExport(asientoDto);
    asientoExportRepository.save(asientoNuevo);

    JSONObject jsonObject = new JSONObject();

    // Estos son fijos y obligatorios
    jsonObject.put("asientoExportId", asientoNuevo.getId());
    jsonObject.put("procesoId", asientoNuevo.getProcesoId());

    // Validar desde aquí cada param adicional para el request body
    if (Objects.nonNull(request.getEmpresaId())) {
      jsonObject.put("empresaId", request.getEmpresaId());
    }

    if (Objects.nonNull(request.getFechaHasta())) {
      jsonObject.put("fechaHasta", request.getFechaHasta());
    }

    asientoNuevo.setRequest(jsonObject.toString());
    asientoExportRepository.saveAndFlush(asientoNuevo);

    return AsientoExportMapper.toAsientoExportResponseDTO(asientoNuevo);
  }

  @Override
  public GenericResponseDTO listarNombres() {
    GenericResponseDTO response = new GenericResponseDTO();
    // TODO esto popula el combo asientos, incorporar filtro x usuario/rol
    List<AsientoDescriptorDTO> listadoNombres = new ArrayList<>();
    listadoNombres.add(obtieneAsientoDeJson(Constants.LIQUISUS));
    listadoNombres.add(obtieneAsientoDeJson(Constants.MOVGASBAN));
    listadoNombres.add(obtieneAsientoDeJson(Constants.MOVIART));
    // listadoNombres.add(obtieneAsientoDeJson(Constants.ORDPAGC))
    listadoNombres.add(obtieneAsientoDeJson(Constants.ORDPAGD));
    // listadoNombres.add(obtieneAsientoDeJson(Constants.PEMPRETC))
    // listadoNombres.add(obtieneAsientoDeJson(Constants.PEMPSUC))
    listadoNombres.add(obtieneAsientoDeJson(Constants.PENSRETC));
    // listadoNombres.add(obtieneAsientoDeJson(Constants.PENSSUC))
    // listadoNombres.add(obtieneAsientoDeJson(Constants.PJCORETC))
    // listadoNombres.add(obtieneAsientoDeJson(Constants.PJCOSUC))
    listadoNombres.add(obtieneAsientoDeJson(Constants.TFCONCEPT));
    listadoNombres.add(obtieneAsientoDeJson(Constants.TFDESCUENTO));
    listadoNombres.add(obtieneAsientoDeJson(Constants.TFDETALLE));

    Collections.sort(listadoNombres);
    response.setEstadoExito(listadoNombres);
    response.setMensaje("" + listadoNombres.size());

    return response;
  }

  private AsientoDescriptorDTO generarPorNombre(AsientoExportGenerarRequestDTO request,
      String nombre) {
    AsientoExportResponseDTO asiento = null;
    AsientoDescriptorDTO result = null;
    JsonNode tipoAsiento = Constants.tablaAsiento.valueOfTableName(nombre);
    asiento = crearAsiento(tipoAsiento, request);
    result = obtieneAsientoDeJson(tipoAsiento);

    if (Objects.nonNull(result)) {
      result.setParent(asiento.getParent());
      result.setAsientoId(asiento.getId());
    }

    return result;
  }

}
