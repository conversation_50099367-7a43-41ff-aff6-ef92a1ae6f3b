package ar.gob.lapampa.nsl.servicioliqprocesos.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.TfDescuenService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/tfdescuen")
public class TfDescuenController {

  @Autowired
  private TfDescuenService tfDescuenService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','TFDESC_R','','IS')")
  public GenericResponseDTO listarTfDescuen(@RequestBody ProcesoResquestDTO procesoRequest) {
    return tfDescuenService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','TFDESC_G','','IS')")
  public GenericResponseDTO generarTfDescuen(@RequestBody GenerarLiquisusRequestDTO request) {
    return tfDescuenService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','TFDESC_R,TFDESC_D','','IS')")
  public GenericResponseDTO borrarTfDescuen(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return tfDescuenService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','TFDESC_EX','','IS')")
  public GenericResponseDTO exportarTfDescuen(
      @RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return tfDescuenService.exportar(procesoRequest);
  }

}
