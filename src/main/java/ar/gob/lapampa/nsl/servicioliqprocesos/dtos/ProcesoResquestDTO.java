package ar.gob.lapampa.nsl.servicioliqprocesos.dtos;

import java.io.Serializable;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ProcesoResquestDTO implements Serializable {

  private static final long serialVersionUID = 7594123660033028122L;

  @NotNull
  private Long asientoExportId;

  @NotNull
  private Long procesoId;

  Long habilitacionId;
  Long juridisccionId;
  Long empresaId;
  Long reciboId;
}
