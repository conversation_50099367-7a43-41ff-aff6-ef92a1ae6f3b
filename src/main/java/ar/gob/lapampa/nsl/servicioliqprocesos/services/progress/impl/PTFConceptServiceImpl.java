package ar.gob.lapampa.nsl.servicioliqprocesos.services.progress.impl;

import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarSuetfconceptRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Suetfconcept;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.progress.TFConcept;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuetfconceptRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.progress.TFConceptRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.SuetfconceptService;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.progress.PTFConceptService;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class PTFConceptServiceImpl implements PTFConceptService {

  private final TFConceptRepository tfconceptRepository;

  public PTFConceptServiceImpl(TFConceptRepository tfconceptRepository) {
    this.tfconceptRepository = tfconceptRepository;
  }

  @Override
  public GenericResponseDTO listar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<TFConcept> result =
        tfconceptRepository.findByExternalId(request.getAsientoExportId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    // String result =
    // sueliquisusRepository.borrarLiquisus(request.getAsientoExportId(), request.getProcesoId());
    // if (result == null) {
    // response.setEstadoExito(List.of());
    // response.setMensaje("No Borrado");
    // } else {
    // response.setEstadoExito(result);
    // }
    return response;
  }

  @Override
  public GenericResponseDTO exportar(@Valid ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
//    String result = tfconceptRepository.exportarTfconcept(request.getAsientoExportId(),
//        request.getProcesoId());
//    if (result == null) {
//      response.setEstadoExito(List.of());
//      response.setMensaje("No Exportado");
//    } else {
//      response.setEstadoExito(result);
//    }
    return response;
  }



}
