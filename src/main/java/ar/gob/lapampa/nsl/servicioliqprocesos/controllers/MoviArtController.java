package ar.gob.lapampa.nsl.servicioliqprocesos.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.SuemoviartService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/moviart")
public class MoviArtController {

  @Autowired
  private SuemoviartService sueMoviArtService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','MOVART_R','','IS')")
  public GenericResponseDTO listarMoviArt(@RequestBody ProcesoResquestDTO procesoRequest) {
    return sueMoviArtService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','MOVART_G','','IS')")
  public GenericResponseDTO generarMoviArt(@RequestBody GenerarLiquisusRequestDTO request) {
    return sueMoviArtService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','MOVART_R,MOVART_D','','IS')")
  public GenericResponseDTO borrarMoviArt(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueMoviArtService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','MOVART_EX','','IS')")
  public GenericResponseDTO exportarMoviArt(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueMoviArtService.exportar(procesoRequest);
  }

}
