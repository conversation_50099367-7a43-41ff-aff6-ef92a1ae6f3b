package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.progress;

import ar.gob.lapampa.nsl.servicioliqprocesos.entities.progress.ConceptoSueldo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Repositorio para la entidad ConceptoSueldo (tabla SUETFCONCEPT)
 */
@Repository
public interface ConceptoSueldoRepository extends JpaRepository<ConceptoSueldo, ConceptoSueldo.ConceptoSueldoId> {

    /**
     * Buscar conceptos por año
     */
    List<ConceptoSueldo> findByAnioCptoOrderByCpto(Integer anioCpto);

    /**
     * Buscar conceptos por tipo
     */
    List<ConceptoSueldo> findByTipoCptoOrderByAnioCptoDescCpto(String tipoCpto);

    /**
     * Buscar conceptos por año y tipo
     */
    List<ConceptoSueldo> findByAnioCptoAndTipoCptoOrderByCpto(Integer anioCpto, String tipoCpto);

    /**
     * Buscar conceptos que contengan texto en la descripción
     */
    @Query("SELECT c FROM ConceptoSueldo c WHERE UPPER(c.descrip) LIKE UPPER(CONCAT('%', :texto, '%')) ORDER BY c.anioCpto DESC, c.cpto")
    List<ConceptoSueldo> findByDescripcionContaining(@Param("texto") String texto);

    /**
     * Buscar conceptos por código específico
     */
    List<ConceptoSueldo> findByCptoOrderByAnioCptoDesc(Integer cpto);

    /**
     * Obtener todos los tipos de concepto únicos
     */
    @Query("SELECT DISTINCT c.tipoCpto FROM ConceptoSueldo c WHERE c.tipoCpto IS NOT NULL ORDER BY c.tipoCpto")
    List<String> findDistinctTiposCpto();

    /**
     * Obtener todos los años disponibles
     */
    @Query("SELECT DISTINCT c.anioCpto FROM ConceptoSueldo c WHERE c.anioCpto IS NOT NULL ORDER BY c.anioCpto DESC")
    List<Integer> findDistinctAnios();

    /**
     * Contar conceptos por tipo y año
     */
    @Query("SELECT c.tipoCpto, COUNT(c) FROM ConceptoSueldo c WHERE c.anioCpto = :anio GROUP BY c.tipoCpto ORDER BY c.tipoCpto")
    List<Object[]> countByTipoAndAnio(@Param("anio") Integer anio);

    /**
     * Buscar conceptos de retenciones (RET) del año actual
     */
    @Query("SELECT c FROM ConceptoSueldo c WHERE c.tipoCpto = 'RET' AND c.anioCpto = :anio ORDER BY c.cpto")
    List<ConceptoSueldo> findRetencionesByAnio(@Param("anio") Integer anio);

    /**
     * Buscar conceptos de deducciones (DED) del año actual
     */
    @Query("SELECT c FROM ConceptoSueldo c WHERE c.tipoCpto = 'DED' AND c.anioCpto = :anio ORDER BY c.cpto")
    List<ConceptoSueldo> findDeduccionesByAnio(@Param("anio") Integer anio);

    /**
     * Buscar aportes patronales (APP) del año actual
     */
    @Query("SELECT c FROM ConceptoSueldo c WHERE c.tipoCpto = 'APP' AND c.anioCpto = :anio ORDER BY c.cpto")
    List<ConceptoSueldo> findAportesPatronalesByAnio(@Param("anio") Integer anio);

    /**
     * Buscar conceptos por rango de códigos
     */
    @Query("SELECT c FROM ConceptoSueldo c WHERE c.anioCpto = :anio AND c.cpto BETWEEN :cptoMin AND :cptoMax ORDER BY c.cpto")
    List<ConceptoSueldo> findByRangoCodigos(@Param("anio") Integer anio, @Param("cptoMin") Integer cptoMin, @Param("cptoMax") Integer cptoMax);
}
