package ar.gob.lapampa.nsl.servicioliqprocesos.services;

import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;

@Service
public interface SuemovgasbanService {

  GenericResponseDTO listar(ProcesoResquestDTO request);

  GenericResponseDTO borrar(ProcesoResquestDTO request);

  GenericResponseDTO exportar(@Valid ProcesoResquestDTO request);

  GenericResponseDTO generar(GenerarLiquisusRequestDTO request);

}
