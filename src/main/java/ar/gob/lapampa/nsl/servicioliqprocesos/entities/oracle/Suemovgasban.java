package ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle;

import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUEMOVGASBAN", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUEMOVGASBAN_SEQ", sequenceName = "LAPAMPA.TEMP_SUEMOVGASBAN_SEQ",
    allocationSize = 1)
public class Suemovgasban {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUEMOVGASBAN_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "ANIOLIQ")
  private Long anioLiq;

  @Column(name = "CARATULA")
  private String caratula;

  @Column(name = "COMPN")
  private Long compN;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "FECHALIQ")
  private Date fechaLiq;

  @Column(name = "FECHAPRO")
  private Date fechaPro;

  @Column(name = "FINFUN")
  private Long finFun;

  @Column(name = "HORA")
  private Long hora;

  @Column(name = "IDLIQ")
  private String idLiq;

  @Column(name = "IMPASIG")
  private BigDecimal impAsig;

  @Column(name = "IMPLIQ")
  private BigDecimal impLiq;

  @Column(name = "MESLIQ")
  private String mesLiq;

  @Column(name = "NROLIQ")
  private String nroLiq;

  @Column(name = "NROPAR")
  private Long nroPar;

  @Column(name = "OPER")
  private String oper;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
