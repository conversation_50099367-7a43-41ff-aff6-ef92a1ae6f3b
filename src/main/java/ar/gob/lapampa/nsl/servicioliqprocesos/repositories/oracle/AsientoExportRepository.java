package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.AsientoExport;

@Repository
public interface AsientoExportRepository extends JpaRepository<AsientoExport, Long> {

  List<AsientoExport> findByProcesoId(Long procesoId);

  List<AsientoExport> findByProcesoIdAndEstado(Long procesoId, String estado);

  List<AsientoExport> findByProcesoIdAndUserLog(Long procesoId, String currentPrincipalName);

  List<AsientoExport> findByUserLog(String currentPrincipalName);

  List<AsientoExport> findByProcesoIdAndEstadoAndUserLog(Long procesoId, String estado,
      String currentPrincipalName);

  List<AsientoExport> findByProcesoIdAndTablaAndUserLog(Long procesoId, String tabla,
      String userLogin);

  List<AsientoExport> findByProcesoIdAndUserLogAndParent(Long procesoId,
      String currentPrincipalName, Long parent);

  List<AsientoExport> findByUserLogAndParent(String currentPrincipalName, Long parent);

  Optional<AsientoExport> findByParentAndTabla(Long id, String tabla);

  @Query(
      //value = "SELECT * FROM lapampa.asientos_exports a WHERE (?1 IS NULL OR a.user_log = ?1) AND a.proceso_id in (?2) AND (?3 IS NULL OR a.tabla = ?3) AND (?4 IS NULL OR a.empresa_id = ?4) AND a.parent IS NULL ORDER BY a.proceso_id, a.nombre DESC",
  		value = "SELECT * FROM lapampa.asientos_exports a WHERE a.proceso_id in (?1) AND (?2 IS NULL OR a.tabla = ?2) AND (?3 IS NULL OR a.empresa_id = ?3) AND a.parent IS NULL ORDER BY a.proceso_id, a.nombre DESC",
      nativeQuery = true)
  //String userLogin se saco el user login
  List<AsientoExport> findByFilter(List<Long> procesos, String tabla, Long empresaId);

  @Query(
      value = "SELECT p.id FROM lapampa.liq_procesos p WHERE (?1 IS NULL OR p.anio = ?1) AND (?2 IS NULL OR p.mes = ?2)",
      nativeQuery = true)
  List<Long> getProcesosId(Integer anio, Integer mes);

  interface ProcesoDescriptor {

    Integer getMes();

    Integer getAnio();

    String getTipoProceso();
  }

  @Query(
      value = "SELECT p.mes, p.anio, t.descripcion FROM liq_procesos p JOIN liq_liquidaciones_tipos t ON p.lit_id = t.id WHERE p.id = ?1",
      nativeQuery = true)
  Optional<ProcesoDescriptor> getProcesoDescriptorByProcesoId(Long procesoId);

  interface EmpresaDescriptor {

    Long getId();

    String getNombre();

    String getDescripcion();
  }

  @Query(
      value = "SELECT DISTINCT RE.ID, (RE.CODIGO || ' - ' || INITCAP(RE.DESCRIPCION)) NOMBRE, RE.DESCRIPCION \r\n"
          + "FROM LAPAMPA.RRHH_OCUPACIONES RO\r\n"
          + "INNER JOIN LAPAMPA.LIQ_OCUPACIONES_RECIBOS LOR ON LOR.OCU_ID = RO.ID\r\n"
          + "INNER JOIN LAPAMPA.RRHH_EMPRESAS RE ON RE.ID = RO.EMP_ID\r\n"
          + "INNER JOIN LAPAMPA.BDU_ORGANISMOS_PRE UOR ON UOR.ID = RO.UOR_ID\r\n"
          + "INNER JOIN LAPAMPA.BDU_ORGANISMOS_PRE JUR ON JUR.ID = UOR.ORP_ID\r\n"
          + "INNER JOIN LAPAMPA.RRHH_HABILITACIONES HAB\r\n" + "               ON HAB.ID =\r\n"
          + "                  (SELECT LAPAMPA.OBTENER_HABILITACION (JUR.CODIGO,\r\n"
          + "                                                        UOR.CODIGO,\r\n"
          + "                                                        RO.ESC_ID)\r\n"
          + "                     FROM DUAL) \r\n" + " WHERE HAB.PEDIDO_FONDO = 'Y'\r\n"
          + " AND LOR.PRC_ID = ?1 ORDER BY RE.DESCRIPCION",
      nativeQuery = true)
  List<EmpresaDescriptor> findAllEmpresaDescriptorByProcesoId(Long procesoId);

  @Query(
      value = "SELECT * FROM \"LAPAMPA\".\"ASIENTOS_EXPORTS\" ae WHERE ae.USER_LOG=:userLog"
          + " AND ae.PROCESO_ID=:procesoId" + " AND (:tabla IS NULL OR ae.TABLA=:tabla)"
          + " AND (:empresaId IS NULL OR JSON_VALUE(ae.REQUEST,'$.empresaId')=:empresaId)",
      nativeQuery = true)
  List<AsientoExport> findAllByUserLogAndProcesoAndEmpresaAndTabla(String userLog, Long procesoId,
      Long empresaId, String tabla);

  @Query(
      value = "SELECT RE.ID, (RE.CODIGO || ' - ' || INITCAP(RE.DESCRIPCION)) NOMBRE, RE.DESCRIPCION FROM \"LAPAMPA\".\"RRHH_EMPRESAS\" RE WHERE RE.ID=:id",
      nativeQuery = true)
  Optional<EmpresaDescriptor> findEmpresaById(Long id);
}
