package ar.gob.lapampa.nsl.servicioliqprocesos.controllers;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootVersion;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * Main Application Controller Defines default check end points
 *
 * @version 1.0.0
 */
@RestController
@RequestMapping
@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class AplicacionController {

  /**
   * application in bootstrap.yml
   */
  @Value("${application}")
  private String application;

  /**
   * Version in pom.xml
   */
  @Value("${project.version}")
  private String version;

  /**
   * Current {@link spring.profiles.active} active profile defined in app yml
   */
  @Value("${spring.profiles.active}")
  private String activeProfile;

  /**
   * Current {@link config-origin} configurations origin
   */
  @Value("${config-origin}")
  private String configOrigin;

  /**
   * environment in boostarp.yml
   */
  @Value("${environment}")
  private String runtimeEnv;

  /**
   * Check de estado
   *
   * @return String
   * 
   */
  @Operation(summary = "Estado del servicio", description = "Devuelve estado del servicio",
      tags = {"Status"}, hidden = false, deprecated = false)
  @GetMapping("/estado")
  public ResponseEntity<String> healthCheck() {

    final StringBuilder msj = new StringBuilder(255);
    final String javaVersion = System.getProperty("java.version");
    final String springVersion = SpringBootVersion.getVersion();
    msj.append(application + ": Ok\nProject version: ").append(version).append("\nJava version: ")
        .append(javaVersion).append("\nProfile activo: ").append(activeProfile)
        .append("\nConfigs desde: ").append(configOrigin).append("\nSpring Boot version: ")
        .append(springVersion).append("\nRuntime Env.: ").append(runtimeEnv);
    return ResponseEntity.ok(msj.toString());
  }

  /**
   * Check logging levels
   *
   * @return String
   */
  @Operation(summary = "Check de logs con lombok",
      description = "Dispara un log de cada tipo para verificar", tags = {"Status"}, hidden = false,
      deprecated = false)
  @GetMapping("/logsTest")
  public String logsCheck() {
    log.trace("Logs check: TRACE Message");
    log.debug("Logs check: DEBUG Message");
    log.info("Logs check: INFO Message");
    log.warn("Logs check: WARN Message");
    log.error("Logs check: ERROR Message");

    return "Ok! Check salida de Logs...";
  }

}
