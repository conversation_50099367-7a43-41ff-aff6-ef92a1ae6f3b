package ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoAsociadoResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoDescriptorDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.AsientoExport;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.AsientoExportRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SueliquisusRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuemovgasbanRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuemoviartRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SueordpagcRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SueordpagdRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuepensretcRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuepenssucRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuetfconceptRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuetfdescuenRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuetfdetalleRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.AsientoExportService;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.AsientoListadorAsociadoService;
import ar.gob.lapampa.nsl.servicioliqprocesos.utils.Constants;

@Service
public class AsientoListadorAsociadoServiceImpl implements AsientoListadorAsociadoService {

  @Autowired
  private AsientoExportRepository asientoExportRepository;

  @Autowired
  private AsientoExportService asientoExportService;

  @Autowired
  private SueordpagdRepository sueordpagdRepository;

  @Autowired
  private SueordpagcRepository sueordpagcRepository;

  @Autowired
  private SueliquisusRepository sueliquisusRepository;

  @Autowired
  private SuemovgasbanRepository suemovgasbanRepository;

  @Autowired
  private SuemoviartRepository suemoviartRepository;

  // @Autowired
  // private SuepempretcRepository suepempretcRepository
  //
  // @Autowired
  // private SuepempsucRepository suepempsucRepository

  @Autowired
  private SuepensretcRepository suepensretcRepository;

  @Autowired
  private SuepenssucRepository suepenssucRepository;

  // @Autowired
  // private SuepjcoretcRepository suepjcoretcRepository
  //
  // @Autowired
  // private SuepjcosucRepository suepjcosucRepository

  @Autowired
  private SuetfconceptRepository suetfconceptRepository;

  @Autowired
  private SuetfdescuenRepository suetfdescuenRepository;

  @Autowired
  private SuetfdetalleRepository suetfdetalleRepository;

  @Override
  public GenericResponseDTO listarAsociadosbyId(Long id) {
    GenericResponseDTO response = new GenericResponseDTO();
    AsientoExport asientoPadre = new AsientoExport();
    List<AsientoAsociadoResponseDTO> lista = new ArrayList<>();
    Optional<AsientoExport> optionalAsiento = asientoExportRepository.findById(id);
    do {
      if (optionalAsiento.isPresent()) {
        asientoPadre = optionalAsiento.get();
        String nombre = asientoPadre.getTabla();
        AsientoAsociadoResponseDTO asociado = obtenerDatos(nombre, id, lista.size() + 1);
        lista.add(asociado);
      } else {
        response.setEstadoExito(List.of());
        response.setMensaje("No encontrado");
      }

      if (Objects.nonNull(asientoPadre.getAsociado())) {
        Optional<AsientoExport> optHijo =
            asientoExportRepository.findByParentAndTabla(id, asientoPadre.getAsociado());
        if (optHijo.isPresent()) {
          id = optHijo.get().getId();
          optionalAsiento = asientoExportRepository.findById(id);
        }
      } else {
        id = 0L;
        response.setEstadoExito(lista);
        response.setMensaje("" + lista.size());
      }
    } while (id != 0L);
    return response;
  }

  private void crearResult(int order, AsientoAsociadoResponseDTO result,
      AsientoDescriptorDTO asientoDescriptor) {
    result.setTabTitle(asientoDescriptor.getGeneralName());
    result.setOrder(order);
    result.setTableHeaders(asientoDescriptor.getTableHeaders());
    result.setHiddenColumns(asientoDescriptor.getHiddenColumns());
  }

  private AsientoAsociadoResponseDTO obtenerDatos(String nombre, Long id, int order) {
    AsientoAsociadoResponseDTO result = new AsientoAsociadoResponseDTO();
    AsientoDescriptorDTO asientoDescriptor =
        asientoExportService.obtieneAsientoDeJson(Constants.ORDPAGD);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(sueordpagdRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.ORDPAGC);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(sueordpagcRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.LIQUISUS);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(sueliquisusRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.MOVGASBAN);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(suemovgasbanRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.MOVIART);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(suemoviartRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    // asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.PEMPRETC)
    // if (asientoDescriptor.getTableName().equals(nombre)) {
    // result.setDatos(suepempretcRepository.findByAsientoExportId(id))
    // crearResult(order, result, asientoDescriptor)
    // }
    //
    // asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.PEMPSUC)
    // if (asientoDescriptor.getTableName().equals(nombre)) {
    // result.setDatos(suepempsucRepository.findByAsientoExportId(id))
    // crearResult(order, result, asientoDescriptor)
    // }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.PENSRETC);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(suepensretcRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.PENSSUC);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(suepenssucRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    // asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.PJCORETC)
    // if (asientoDescriptor.getTableName().equals(nombre)) {
    // result.setDatos(suepjcoretcRepository.findByAsientoExportId(id))
    // crearResult(order, result, asientoDescriptor)
    // }
    //
    // asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.PJCOSUC)
    // if (asientoDescriptor.getTableName().equals(nombre)) {
    // result.setDatos(suepjcosucRepository.findByAsientoExportId(id))
    // crearResult(order, result, asientoDescriptor)
    // }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.TFCONCEPT);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(suetfconceptRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.TFDESCUENTO);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(suetfdescuenRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    asientoDescriptor = asientoExportService.obtieneAsientoDeJson(Constants.TFDETALLE);
    if (asientoDescriptor.getTableName().equals(nombre)) {
      result.setDatos(suetfdetalleRepository.findByAsientoExportId(id));
      crearResult(order, result, asientoDescriptor);
    }

    return result;
  }

}
