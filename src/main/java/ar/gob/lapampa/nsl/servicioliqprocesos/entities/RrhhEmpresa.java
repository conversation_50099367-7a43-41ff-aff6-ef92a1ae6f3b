package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_EMPRESAS")
@SequenceGenerator(name = "RRHH_EMP_SEQ", sequenceName = "RRHH_EMP_SEQ", allocationSize = 1)
public class RrhhEmpresa implements Serializable {

  private static final long serialVersionUID = -3239824324921150902L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_EMP_SEQ")
  private Long id;

  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Size(min = 1, max = 200)
  @Column(name = "CODIGO")
  private String codigo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Boolean activo;

}
