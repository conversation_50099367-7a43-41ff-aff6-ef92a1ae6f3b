package ar.gob.lapampa.nsl.servicioliqprocesos.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.SuemovgasbanService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/movgasban")
public class MovGasBanController {

  @Autowired
  private SuemovgasbanService suemovgasbanService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','MGASBAN_R','','IS')")
  public GenericResponseDTO listarMovGasBan(@RequestBody ProcesoResquestDTO procesoRequest) {
    return suemovgasbanService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','MGASBAN_G','','IS')")
  public GenericResponseDTO generarMovGasBan(@RequestBody GenerarLiquisusRequestDTO request) {
    return suemovgasbanService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','MGASBAN_R,MGASBAN_D','','IS')")
  public GenericResponseDTO borrarMovGasBan(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suemovgasbanService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','MGASBAN_EX','','IS')")
  public GenericResponseDTO exportarMovGasBan(
      @RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suemovgasbanService.exportar(procesoRequest);
  }

}
