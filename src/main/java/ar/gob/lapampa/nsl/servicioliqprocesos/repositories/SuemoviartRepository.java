package ar.gob.lapampa.nsl.servicioliqprocesos.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Suemoviart;

@Repository
public interface SuemoviartRepository extends JpaRepository<Suemoviart, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_MOVIART_GENERAR")
  String obtenerMoviart(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_MOVIART_EXPORTAR")
  String exportarMoviart(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_MOVIART_ELIMINAR")
  String borrarMoviart(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  List<Suemoviart> findByAsientoExportId(Long asientoExportId);

}

