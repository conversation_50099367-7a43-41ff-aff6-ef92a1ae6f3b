package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.oracle;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportFilterRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportGenerarRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.AsientoExportService;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.AsientoListadorAsociadoService;

@RestController
@RequestMapping("/api/v1/asientos")
public class AsientosController {

  @Autowired
  private AsientoExportService asientoExportService;

  @Autowired
  private AsientoListadorAsociadoService asientoListadorAsociadoService;

  @PostMapping("/listadoProcesos")
  @PreAuthorize("@cs.hasAccess('','ASIENTO_R','','IS')")
  public GenericResponseDTO listarProcesos(@RequestBody AsientoExportRequestDTO asientoRequest) {
    return asientoExportService.listaProcesos(asientoRequest);
  }

  @PostMapping("/generarProcesos")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','ASIENTO_G','','IS')")
  public GenericResponseDTO generarProcesos(
      @RequestBody AsientoExportGenerarRequestDTO asientoRequest) {
    return asientoExportService.generarProcesos(asientoRequest);
  }

  @GetMapping("/listarTodos")
  @PreAuthorize("@cs.hasAccess('','ASIENTO_R','','IS')")
  public GenericResponseDTO listarTodos() {
    return asientoExportService.listaProcesos(new AsientoExportRequestDTO());
  }

  @GetMapping()
  @PreAuthorize("@cs.hasAccess('','ASIENTO_R','','IS')")
  public GenericResponseDTO listarPorId(@RequestParam(required = true) Long id) {
    return asientoExportService.byId(id);
  }

  @PostMapping("/listarAsientosContablesFiltrado")
  @PreAuthorize("@cs.hasAccess('','ASIENTO_R','','IS')")
  public GenericResponseDTO listarProcesosFiltrados(
      @RequestBody AsientoExportFilterRequestDTO filterRequest) {
    return asientoExportService.listaProcesosFiltrado(filterRequest);
  }

  @GetMapping("/listarNombres")
  @PreAuthorize("@cs.hasAccess('','ASIENTO_R','','IS')")
  public GenericResponseDTO listarNombres() {
    return asientoExportService.listarNombres();
  }

  @GetMapping("/listarAsociados")
  @PreAuthorize("@cs.hasAccess('','ASIENTO_R','','IS')")
  public GenericResponseDTO listarAsociados(@RequestParam(required = true) Long id) {
    return asientoListadorAsociadoService.listarAsociadosbyId(id);
  }

  @GetMapping("/listarEmpresas")
  @PreAuthorize("@cs.hasAccess('','ASIENTO_R','','IS')")
  public GenericResponseDTO listarEmpresas(@RequestParam(required = true) Long id) {
    return asientoExportService.listaEmpresasPorProcesoId(id);
  }
}
