package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.oracle;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarSuetfconceptRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.SuetfconceptService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/tfconcept")
public class TfConceptController {

  @Autowired
  private SuetfconceptService suetfconceptService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','TFCON_R','','IS')")
  public GenericResponseDTO listarTfConcept(@RequestBody ProcesoResquestDTO procesoRequest) {
    return suetfconceptService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','TFCON_G','','IS')")
  public GenericResponseDTO generarTfConcept(@RequestBody GenerarSuetfconceptRequestDTO request) {
    return suetfconceptService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','TFCON_R, TFCON_D','','IS')")
  public GenericResponseDTO borrarTfConcept(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suetfconceptService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','TFCON_EX','','IS')")
  public GenericResponseDTO exportarTfConcept(
      @RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suetfconceptService.exportar(procesoRequest);
  }

}
