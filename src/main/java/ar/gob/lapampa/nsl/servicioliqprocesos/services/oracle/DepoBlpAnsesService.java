package ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle;

import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.BlpAnsesResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarBlpAnsesRequestDTO;

@Service
public interface DepoBlpAnsesService {

  GenericResponseDTO generar(GenerarBlpAnsesRequestDTO request);

  GenericResponseDTO listar(BlpAnsesResquestDTO request);

  GenericResponseDTO listarDetalles(BlpAnsesResquestDTO blpAnsesResquestDTO);

}
