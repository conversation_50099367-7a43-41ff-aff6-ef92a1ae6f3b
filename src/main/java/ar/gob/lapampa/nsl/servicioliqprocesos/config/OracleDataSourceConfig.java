package ar.gob.lapampa.nsl.servicioliqprocesos.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EnableJpaRepositories(
    basePackages = "ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle",
    entityManagerFactoryRef = "oracleEntityManagerFactory",
    transactionManagerRef = "oracleTransactionManager"
)
public class OracleDataSourceConfig {
    // Esta clase solo define la configuración de repositorios para Oracle
    // Los beans están definidos en DataSourceConfig
}
