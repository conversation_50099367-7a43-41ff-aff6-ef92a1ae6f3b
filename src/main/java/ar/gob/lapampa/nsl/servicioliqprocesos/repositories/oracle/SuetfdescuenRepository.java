package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle;

import java.util.List;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Suetfdescuen;

@Repository
public interface SuetfdescuenRepository extends JpaRepository<Suetfdescuen, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_TFDESCUEN_GENERAR")
  String obtenerSuetfdescuen(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_TFDESCUEN_EXPORTAR")
  String exportarSuetfdescuen(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_TFDESCUEN_ELIMINAR")
  String borrarSuetfdescuen(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Query("SELECT s FROM Suetfdescuen s WHERE s.asientoExportId = :asientoExportId")
  List<Suetfdescuen> findByAsientoExportId(@Param("asientoExportId") Long asientoExportId);

}
