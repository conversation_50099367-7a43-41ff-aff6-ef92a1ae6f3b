package ar.gob.lapampa.nsl.servicioliqprocesos.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.AsientoLog;

@Repository
public interface AsientoLogRepository extends JpaRepository<AsientoLog, Long> {

  List<AsientoLog> findByAsientoId(Long asientoId);

  List<AsientoLog> findByAsientoIdAndEstado(Long asientoId, String estado);

  List<AsientoLog> findByAsientoIdAndNombre(Long asientoId, String nombre);

  List<AsientoLog> findByEstado(String estado);

  List<AsientoLog> findByAsientoIdAndEstadoAndNombre(Long asientoId, String estado, String nombre);

  List<AsientoLog> findByNombre(String nombre);

}
