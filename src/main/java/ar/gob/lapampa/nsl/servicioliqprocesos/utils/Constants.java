package ar.gob.lapampa.nsl.servicioliqprocesos.utils;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class Constants {

  public static final JsonNode LIQUISUS = readResourceJson("liquisus.json");
  public static final JsonNode MOVGASBAN = readResourceJson("movgasban.json");
  public static final JsonNode MOVIART = readResourceJson("moviart.json");
  public static final JsonNode ORDPAGC = readResourceJson("ordpagc.json");
  public static final JsonNode ORDPAGD = readResourceJson("ordpagd.json");
  public static final JsonNode PEMPRETC = readResourceJson("pempretc.json");
  public static final JsonNode PEMPSUC = readResourceJson("pempsuc.json");
  public static final JsonNode PENSRETC = readResourceJson("pensretc.json");
  public static final JsonNode PENSSUC = readResourceJson("penssuc.json");
  public static final JsonNode PJCORETC = readResourceJson("pjcoretc.json");
  public static final JsonNode PJCOSUC = readResourceJson("pjcosuc.json");
  public static final JsonNode TFCONCEPT = readResourceJson("tfconcept.json");
  public static final JsonNode TFDESCUENTO = readResourceJson("tfdescuento.json");
  public static final JsonNode TFDETALLE = readResourceJson("tfdetalle.json");

  private static JsonNode readResourceJson(String resource) {
    try (InputStream inputStream =
        Thread.currentThread().getContextClassLoader().getResourceAsStream("json/" + resource)) {
      ObjectMapper mapper = new ObjectMapper();
      return mapper.readValue(inputStream, JsonNode.class);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  public enum Status {
 // @formatter:off
    ENCOLADO("Encolado", 1L),
    EJECUTANDO("Ejecutando", 2L),
    FINALIZADO("Finalizado", 3L),
    DESCARGADO("Descargado", 4L),
    BORRADO("Borrado", 5L),
    EXPORTANDO("Exportando", 6L),
    TESTEADO("Testeado", 7L),
    ERROR("Error", -1L);
 // @formatter:on
    private static final Map<String, Status> BY_LABEL = new HashMap<>();
    private static final Map<Long, Status> BY_NUMBER = new HashMap<>();

    static {
      for (Status s : values()) {
        BY_LABEL.put(s.label, s);
        BY_NUMBER.put(s.number, s);
      }
    }

    public final String label;
    public final Long number;

    private Status(String label, Long number) {
      this.label = label;
      this.number = number;
    }

    public static Status valueOfLabel(String label) {
      return BY_LABEL.get(label);
    }

    public static Status valueOfNumber(Long number) {
      return BY_NUMBER.get(number);
    }
  }

  public enum tablaAsiento {
 // @formatter:off
    SUE_LIQUISUS("liquisus", LIQUISUS),
    SUE_MOVGASBAN("movgasban", MOVGASBAN),
    SUE_MOVIART("moviart", MOVIART),
    SUE_ORDPAGC("ordpagc", ORDPAGC),
    SUE_ORDPAGD("ordpagd", ORDPAGD),
    SUE_PEMPRETC("pempretc", PEMPRETC),
    SUE_PEMPSUC("pempsuc", PEMPSUC),
    SUE_PENSRETC("pensretc", PENSRETC),
    SUE_PENSSUC("penssuc", PENSSUC),
    SUE_PJCORETC("pjcoretc", PJCORETC),
    SUE_PJCOSUC("pjcosuc", PJCOSUC),
    SUE_TFCONCEPT("tfconcept", TFCONCEPT),
    SUE_TFDESCUENTO("tfdescuen", TFDESCUENTO),
    SUE_TFDETALLE("tfdetalle", TFDETALLE);
 // @formatter:on
    private static final Map<String, JsonNode> BY_TABLENAME = new HashMap<>();
    private static final Map<JsonNode, String> BY_NODE = new HashMap<>();

    static {
      for (tablaAsiento s : values()) {
        BY_TABLENAME.put(s.tableName, s.node);
        BY_NODE.put(s.node, s.tableName);
      }
    }

    public final String tableName;
    public final JsonNode node;

    private tablaAsiento(String tableName, JsonNode node) {
      this.tableName = tableName;
      this.node = node;
    }

    public static JsonNode valueOfTableName(String tableName) {
      return BY_TABLENAME.get(tableName);
    }

    public static String valueOfNode(JsonNode node) {
      return BY_NODE.get(node);
    }
  }
}
