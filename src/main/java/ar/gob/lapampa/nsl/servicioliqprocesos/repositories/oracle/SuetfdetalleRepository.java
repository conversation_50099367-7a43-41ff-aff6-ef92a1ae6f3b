package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle;

import java.util.List;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Suetfdetalle;

@Repository
public interface SuetfdetalleRepository extends JpaRepository<Suetfdetalle, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_TFDETALLE_GENERAR")
  String obtenerSuetfdetalle(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_TFDETALLE_EXPORTAR")
  String exportarSuetfdetalle(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_TFDETALLE_ELIMINAR")
  String borrarSuetfdetalle(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  List<Suetfdetalle> findByAsientoExportId(Long asientoExportId);

}
