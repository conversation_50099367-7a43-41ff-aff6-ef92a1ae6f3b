package ar.gob.lapampa.nsl.servicioliqprocesos.repositories;

import java.sql.Date;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Suepensretc;

@Repository
public interface SuepensretcRepository extends JpaRepository<Suepensretc, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_PENSRETC_GENERAR")
  String obtenerPensRetC(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_PENSRETC_EXPORTAR")
  String exportarPensRetC(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_PENSRETC_ELIMINAR")
  String borrarPensRetC(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  List<Suepensretc> findByAsientoExportId(Long asientoExportId);

}
