package ar.gob.lapampa.nsl.servicioliqprocesos.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Suepenssuc;

@Repository
public interface SuepenssucRepository extends JpaRepository<Suepenssuc, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_PENSSUC_GENERAR")
  String obtenerPensSuc(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_PENSSUC_EXPORTAR")
  String exportarPensSuc(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_PENSSUC_ELIMINAR")
  String borrarPensSuc(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  List<Suepenssuc> findByAsientoExportId(Long asientoExportId);

}
