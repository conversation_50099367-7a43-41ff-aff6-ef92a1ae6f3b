package ar.gob.lapampa.nsl.servicioliqprocesos.dtos;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AsientoExportGenerarRequestDTO implements Serializable {

  private static final long serialVersionUID = 7594121237563028122L;

  @Nonnull
  private String userLogin;

  private Integer anio;

  private Integer mes;

  @Nonnull
  private Long procesoId;

  @Nonnull
  private String nombre;

  private Long empresaId;

  private String estado;

  private String fechaHasta;
  
  private Long instancia;
  private Long insId;
  private String tipoProceso;

}
