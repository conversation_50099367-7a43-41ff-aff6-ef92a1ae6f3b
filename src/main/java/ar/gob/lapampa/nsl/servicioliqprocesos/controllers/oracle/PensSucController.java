package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.oracle;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.SuePensSucService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/penssuc")
public class PensSucController {

  @Autowired
  private SuePensSucService suePensSucService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','PENSUC_R','','IS')")
  public GenericResponseDTO listarPensSuc(@RequestBody ProcesoResquestDTO procesoRequest) {
    return suePensSucService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','PENSUC_G','','IS')")
  public GenericResponseDTO generarPensSuc(@RequestBody GenerarLiquisusRequestDTO request) {
    return suePensSucService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','PENSUC_R,PENSUC_D','','IS')")
  public GenericResponseDTO borrarPensSuc(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suePensSucService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','PENSUC_EX','','IS')")
  public GenericResponseDTO exportarPensSuc(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suePensSucService.exportar(procesoRequest);
  }

}
