package ar.gob.lapampa.nsl.servicioliqprocesos.services.impl;

import java.util.List;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarOrdPagRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Sueordpagc;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SueordpagcRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SueordpagdRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.SueOrdPagCService;

@Service
public class SueOrdPagCServiceImpl implements SueOrdPagCService {

  private SueordpagcRepository sueordpagcRepository;
  private SueordpagdRepository sueordpagdRepository;

  public SueOrdPagCServiceImpl(SueordpagcRepository sueordpagcRepository) {
    this.sueordpagcRepository = sueordpagcRepository;
  }

  @Override
  public GenericResponseDTO listar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<Sueordpagc> result =
        sueordpagcRepository.findByAsientoExportId(request.getAsientoExportId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

  @Override
  public GenericResponseDTO generar(GenerarOrdPagRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    Long asientoExportId = request.getAsientoExportId();
    Long procesoId = request.getProcesoId();
    Long empresaId = request.getEmpresaId();

    String result = sueordpagdRepository.obtenerOrdpagd(asientoExportId, procesoId, empresaId);
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Obtenido");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result =
        sueordpagcRepository.borrarOrdpagc(request.getAsientoExportId(), request.getProcesoId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Borrado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

  @Override
  public GenericResponseDTO exportar(@Valid ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result = sueordpagcRepository.exportarOrdpagc(request.getAsientoExportId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Exportado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }
}
