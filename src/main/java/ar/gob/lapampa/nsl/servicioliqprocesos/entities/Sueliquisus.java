package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUELIQUISUS", schema = "LAPAMPA")
public class Sueliquisus {
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "ANIO")
  private Long anio;

  @Column(name = "CAR")
  private String car;

  @Column(name = "CODOP")
  private String codop;

  @Column(name = "CPAG")
  private Long cpag;

  @Column(name = "CTABAN")
  private Long ctaban;

  @Column(name = "CUENTA")
  private Long cuenta;

  @Column(name = "DIVEXTRA")
  private Long divextra;

  @Column(name = "EXPEDA")
  private Long expeda;

  @Column(name = "EXPEDI")
  private String expedi;

  @Column(name = "EXPEDN")
  private String expedn;

  @Column(name = "FECCONF")
  private Date fecconf;

  @Column(name = "FECHA")
  private Date fecha;

  @Column(name = "FECORI")
  private Date fecori;

  @Column(name = "HORA")
  private Long hora;

  @Column(name = "IDLIQ")
  private String idliq;

  @Column(name = "IMPORTE")
  private BigDecimal importe;

  @Column(name = "JUR")
  private String jur;

  @Column(name = "LIQANIO")
  private Long liqanio;

  @Column(name = "LIQNRO")
  private String liqnro;

  @Column(name = "LIQSUB")
  private Long liqsub;

  @Column(name = "LIQUINT")
  private String liquint;

  @Column(name = "NEGANIO")
  private Long neganio;

  @Column(name = "NEGINT")
  private String negint;

  @Column(name = "NEGNRO")
  private String negnro;

  @Column(name = "NROPAR")
  private Long nropar;

  @Column(name = "NROPROV")
  private Long nroprov;

  @Column(name = "OPER")
  private String oper;

  @Column(name = "PPRI")
  private Long ppri;

  @Column(name = "RENGLON")
  private Long renglon;

  @Column(name = "TIPMOV")
  private String tipmov;

  @Column(name = "MARCACOPIADO")
  private String marcacopiado;

}
