package ar.gob.lapampa.nsl.servicioliqprocesos.dtos;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AsientoExportResponseDTO implements Serializable {

  private static final long serialVersionUID = 7594841660033028122L;
  private Long id;
  private String campos;
  private Date fechaCreado = new Date();
  private Long procesoId;
  private Long procesoRegistros = 0L;
  private Date procesoInicio;
  private Date procesoFinal;
  private String resultado;
  private Long procesoErrores = 0L;
  private String procedure;
  private String nombre;
  private String estado;
  private String cabeceras;
  private String url;
  private String request;
  private String userLog;
  private Date exportado;
  private Integer anio;
  private Integer mes;
  private String tipoProceso;
  private String exportable;
  private String tabla;
  private String asociado;
  private Long parent;
  // private Date fechaHasta;
  private RrhhEmpresaDTO empresa;
  // Calculated
  private Long duration; // in milliseconds
  private String elapsed;
  private Long instancia;
  private Long insId;
}
