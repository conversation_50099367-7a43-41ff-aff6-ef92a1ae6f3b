package ar.gob.lapampa.nsl.servicioliqprocesos.repositories;

import java.sql.Date;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.LiqDepositoBlpAnses;

@Repository
public interface DepoBlpAnsesRepository extends JpaRepository<LiqDepositoBlpAnses, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.DEPOSITOBLP_ANSES")
  void obtenerBlpAnses(@Param("P_PRC_ID") Long procesoId, @Param("P_FECHA_HASTA") Date fechaHasta);

  List<LiqDepositoBlpAnses> findByProcesoId(Long procesoId);

}
