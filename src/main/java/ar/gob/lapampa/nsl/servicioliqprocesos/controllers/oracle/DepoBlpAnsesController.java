package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.oracle;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.BlpAnsesResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarBlpAnsesRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.DepoBlpAnsesService;

@RestController
@RequestMapping("/api/v1/depoblpanses")
public class DepoBlpAnsesController {

  @Autowired
  private DepoBlpAnsesService depoBlpAnsesService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','BLANSES_R','','IS')")
  public GenericResponseDTO listarBplAnses(@RequestBody BlpAnsesResquestDTO blpAnsesResquestDTO) {
    return depoBlpAnsesService.listar(blpAnsesResquestDTO);
  }

  @PostMapping("/listarDetalles")
  @PreAuthorize("@cs.hasAccess('','BLANSES_R','','IS')")
  public GenericResponseDTO listarBplAnsesDet(
      @RequestBody BlpAnsesResquestDTO blpAnsesResquestDTO) {
    return depoBlpAnsesService.listarDetalles(blpAnsesResquestDTO);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','BLANSES_G','','IS')")
  public GenericResponseDTO generarBlpAnses(@RequestBody GenerarBlpAnsesRequestDTO request) {
    return depoBlpAnsesService.generar(request);
  }


}
