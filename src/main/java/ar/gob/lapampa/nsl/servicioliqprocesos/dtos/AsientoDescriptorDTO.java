package ar.gob.lapampa.nsl.servicioliqprocesos.dtos;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AsientoDescriptorDTO implements Serializable, Comparable<AsientoDescriptorDTO> {

  private static final long serialVersionUID = 5059702463411363546L;
  private String generalName;
  private String generarURL;
  private String exportarURL;
  private String tableName;
  private String asociado;
  private Long parent;
  private Long asientoId;
  private List<ProcedureParameterDTO> fieldParameters;
  private List<HeaderParameterDTO> tableHeaders;
  private List<String> hiddenColumns;

  @Override
  public int compareTo(AsientoDescriptorDTO otherDescriptor) {
    return this.getGeneralName().compareTo(otherDescriptor.getGeneralName());
  }

  @Override
  public int hashCode() {
    return Objects.hash(tableName);
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }
    if (obj == null) {
      return false;
    }
    if (getClass() != obj.getClass()) {
      return false;
    }
    AsientoDescriptorDTO other = (AsientoDescriptorDTO) obj;
    return Objects.equals(generalName, other.generalName);
  }

}
