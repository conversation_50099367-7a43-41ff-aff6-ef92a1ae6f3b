package ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle;

import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "ASIENTOS_LOGS", schema = "LAPAMPA")
@SequenceGenerator(name = "ASIENTOS_LOGS_SEQ", sequenceName = "LAPAMPA.ASIENTOS_LOGS_ID_SEQ",
    allocationSize = 1)
public class AsientoLog implements Serializable {

  private static final long serialVersionUID = -2966941520212189302L;

  @Id
  @Basic(optional = false)
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ASIENTOS_LOGS_SEQ")
  private Long id;

  @Column(name = "CREADO")
  private Date fechaCreado;

  @Column(name = "ASIENTO_EXPORT_ID")
  private Long asientoId;

  @Column(name = "RESULTADO")
  private String resultado;

  @Column(name = "NOMBRE")
  private String nombre;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "COMENTARIO")
  private String comentario;


}
