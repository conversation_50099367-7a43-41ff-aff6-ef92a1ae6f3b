package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUETFCONCEPT", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUETFCONCEPT_SEQ", sequenceName = "LAPAMPA.TEMP_SUETFCONCEPT_SEQ",
    allocationSize = 1)
public class Suetfconcept {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUETFCONCEPT_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "ANIOCPTO")
  private Long anioCpto;

  @Column(name = "CPTO")
  private Long cpto;

  @Column(name = "DESCRIP")
  private String descrip;

  @Column(name = "MESCPTO")
  private Long mesCpto;

  @Column(name = "TIPOCPTO")
  private String tipoCpto;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
