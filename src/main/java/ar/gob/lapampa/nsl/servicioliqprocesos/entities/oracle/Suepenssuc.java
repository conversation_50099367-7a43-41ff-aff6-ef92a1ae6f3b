package ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle;

import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUEPENSSUC", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUEPENSSUC_SEQ", sequenceName = "LAPAMPA.TEMP_SUEPENSSUC_SEQ",
    allocationSize = 1)
public class Suepenssuc {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUEPENSSUC_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "ANIOL")
  private Long aniol;

  @Column(name = "MESL")
  private Long mesl;

  @Column(name = "NMBR")
  private String nmbr;

  @Column(name = "PRCUIL")
  private Long prcuil;

  @Column(name = "SUCU")
  private Long sucu;

  @Column(name = "TLIQ")
  private String tliq;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
