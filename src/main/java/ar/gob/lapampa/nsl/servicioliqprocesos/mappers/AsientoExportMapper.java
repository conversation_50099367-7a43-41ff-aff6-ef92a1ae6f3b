package ar.gob.lapampa.nsl.servicioliqprocesos.mappers;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.AsientoExport;


public class AsientoExportMapper {

  private AsientoExportMapper() {}

  public static AsientoExportResponseDTO

      toAsientoExportResponseDTO(AsientoExport asientoExport) {

    if (asientoExport == null) {
      return null;
    }

    AsientoExportResponseDTO asientoExportResponseDTO = new AsientoExportResponseDTO();

    asientoExportResponseDTO.setId(asientoExport.getId());
    asientoExportResponseDTO.setCampos(asientoExport.getCampos());
    asientoExportResponseDTO.setProcesoId(asientoExport.getProcesoId());
    asientoExportResponseDTO.setFechaCreado(asientoExport.getFechaCreado());
    asientoExportResponseDTO.setProcedure(asientoExport.getProcedure());
    asientoExportResponseDTO.setNombre(asientoExport.getNombre());
    asientoExportResponseDTO.setResultado(asientoExport.getResultado());
    asientoExportResponseDTO.setProcesoRegistros(asientoExport.getProcesoRegistros());
    asientoExportResponseDTO.setProcesoErrores(asientoExport.getProcesoErrores());
    asientoExportResponseDTO.setProcesoInicio(asientoExport.getProcesoInicio());
    asientoExportResponseDTO.setProcesoFinal(asientoExport.getProcesoFinal());
    asientoExportResponseDTO.setEstado(asientoExport.getEstado());
    asientoExportResponseDTO.setCabeceras(asientoExport.getCabeceras());
    asientoExportResponseDTO.setUrl(asientoExport.getUrl());
    asientoExportResponseDTO.setRequest(asientoExport.getRequest());
    asientoExportResponseDTO.setUserLog(asientoExport.getUserLog());
    asientoExportResponseDTO.setExportado(asientoExport.getExportado());
    asientoExportResponseDTO.setAnio(asientoExport.getAnio());
    asientoExportResponseDTO.setMes(asientoExport.getMes());
    asientoExportResponseDTO.setTipoProceso(asientoExport.getTipoProceso());
    asientoExportResponseDTO.setExportable(asientoExport.getExportable());
    asientoExportResponseDTO.setTabla(asientoExport.getTabla());
    asientoExportResponseDTO.setAsociado(asientoExport.getAsociado());
    asientoExportResponseDTO.setParent(asientoExport.getParent());
    asientoExportResponseDTO.setInsId(asientoExport.getInsId());
    
    asientoExportResponseDTO
        .setEmpresa(RrrhhEmpresaMapper.toRrhhEmpresaDTO(asientoExport.getEmpresa()));
    // Calculated
    
    Instant endInstant = Instant.now();
    if (asientoExport.getProcesoFinal() != null) {
      endInstant = asientoExport.getProcesoFinal().toInstant();
    }
    Instant startInstant = asientoExport.getFechaCreado().toInstant();
    if (asientoExport.getProcesoInicio() != null) {
      startInstant = asientoExport.getProcesoInicio().toInstant();
    }
    Duration duration = Duration.between(startInstant, endInstant);
    if (endInstant.isBefore(startInstant)) {
      duration = Duration.between(endInstant, startInstant);
    }
    asientoExportResponseDTO.setDuration(duration.toMillis());
    StringBuilder sb = new StringBuilder();
    sb.append(duration.toDaysPart());
    sb.append(":");
    sb.append(duration.toHoursPart());
    sb.append(":");
    sb.append(duration.toMinutesPart());
    sb.append(":");
    sb.append(duration.toSecondsPart());
    sb.append(":");
    sb.append(duration.toMillisPart());
    asientoExportResponseDTO.setElapsed(sb.toString());

    return asientoExportResponseDTO;
  }

  public static List<AsientoExportResponseDTO> toAsientoExportResponseDTOList(
      List<AsientoExport> list) {
    if (list == null) {
      return Collections.emptyList();
    }

    List<AsientoExportResponseDTO> list1 = new ArrayList<>(list.size());
    for (AsientoExport asientoExport : list) {
      list1.add(toAsientoExportResponseDTO(asientoExport));
    }
    return list1;
  }

  public static AsientoExport

      toAsientoExport(AsientoExportResponseDTO asientoExportResponseDTO) {

    if (asientoExportResponseDTO == null) {
      return null;
    }

    AsientoExport asientoExport = new AsientoExport();

    asientoExport.setId(asientoExportResponseDTO.getId());
    asientoExport.setCampos(asientoExportResponseDTO.getCampos());
    asientoExport.setProcesoId(asientoExportResponseDTO.getProcesoId());
    asientoExport.setFechaCreado(asientoExportResponseDTO.getFechaCreado());
    asientoExport.setProcedure(asientoExportResponseDTO.getProcedure());
    asientoExport.setNombre(asientoExportResponseDTO.getNombre());
    asientoExport.setResultado(asientoExportResponseDTO.getResultado());
    asientoExport.setProcesoRegistros(asientoExportResponseDTO.getProcesoRegistros());
    asientoExport.setProcesoErrores(asientoExportResponseDTO.getProcesoErrores());
    asientoExport.setProcesoInicio(asientoExportResponseDTO.getProcesoInicio());
    asientoExport.setProcesoFinal(asientoExportResponseDTO.getProcesoFinal());
    asientoExport.setEstado(asientoExportResponseDTO.getEstado());
    asientoExport.setCabeceras(asientoExportResponseDTO.getCabeceras());
    asientoExport.setUrl(asientoExportResponseDTO.getUrl());
    asientoExport.setRequest(asientoExportResponseDTO.getRequest());
    asientoExport.setUserLog(asientoExportResponseDTO.getUserLog());
    asientoExport.setExportado(asientoExportResponseDTO.getExportado());
    asientoExport.setAnio(asientoExportResponseDTO.getAnio());
    asientoExport.setMes(asientoExportResponseDTO.getMes());
    asientoExport.setTipoProceso(asientoExportResponseDTO.getTipoProceso());
    asientoExport.setExportable(asientoExportResponseDTO.getExportable());
    asientoExport.setTabla(asientoExportResponseDTO.getTabla());
    asientoExport.setAsociado(asientoExportResponseDTO.getAsociado());
    asientoExport.setParent(asientoExportResponseDTO.getParent());
    asientoExport
        .setEmpresa(RrrhhEmpresaMapper.toRrhhEmpresa(asientoExportResponseDTO.getEmpresa()));
    asientoExport.setInsId(asientoExportResponseDTO.getInsId());
    return asientoExport;
  }

  public static List<AsientoExport> toAsientoExportList(List<AsientoExportResponseDTO> list) {
    if (list == null) {
      return Collections.emptyList();
    }

    List<AsientoExport> list1 = new ArrayList<>(list.size());
    for (AsientoExportResponseDTO asientoExportResponseDTO : list) {
      list1.add(toAsientoExport(asientoExportResponseDTO));
    }
    return list1;
  }
}
