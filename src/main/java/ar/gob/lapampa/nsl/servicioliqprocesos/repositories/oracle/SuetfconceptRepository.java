package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Suetfconcept;

@Repository
public interface SuetfconceptRepository extends JpaRepository<Suetfconcept, Long> {
	  @Transactional
	  @Modifying
	  @Procedure(value = "LAPAMPA.ASIENTOS_TFCONCEPT_GENERAR")
	  String obtenerTfconcept(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
	      @Param("P_PROCESO_ID") Long procesoId);
	  
	  @Transactional
	  @Modifying
	  @Procedure(value = "LAPAMPA.ASIENTOS_TFCONCEPT_EXPORTAR")
	  String exportarTfconcept(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
	      @Param("P_PROCESO_ID") Long procesoId);
	  
	  List<Suetfconcept> findByAsientoExportId(Long asientoExportId);
}