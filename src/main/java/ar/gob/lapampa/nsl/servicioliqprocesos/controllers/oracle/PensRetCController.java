package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.oracle;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarPensRetRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.SuePensRetCService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/pensretc")
public class PensRetCController {

  @Autowired
  private SuePensRetCService suePensRetCService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','PENSRET_R','','IS')")
  public GenericResponseDTO listarPensretc(@RequestBody ProcesoResquestDTO procesoRequest) {
    return suePensRetCService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','PENSRET_G','','IS')")
  public GenericResponseDTO generarPensretc(@RequestBody GenerarPensRetRequestDTO request) {
    return suePensRetCService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','PENSRET_R,PENSRET_D','','IS')")
  public GenericResponseDTO borrarPensretc(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suePensRetCService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','PENSRET_EX','','IS')")
  public GenericResponseDTO exportarPensretc(
      @RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return suePensRetCService.exportar(procesoRequest);
  }

}
