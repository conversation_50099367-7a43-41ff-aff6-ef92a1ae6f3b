package ar.gob.lapampa.nsl.servicioliqprocesos.config;

import org.hibernate.dialect.DatabaseVersion;
import org.hibernate.dialect.PostgreSQLDialect;

/**
 * Dialecto simplificado para Progress OpenEdge Database
 * Extiende PostgreSQLDialect ya que Progress tiene similitudes con PostgreSQL
 * en términos de sintaxis SQL básica
 */
public class ProgressDialectSimple extends PostgreSQLDialect {

    public ProgressDialectSimple() {
        super(DatabaseVersion.make(12, 0));
    }

    public ProgressDialectSimple(DatabaseVersion version) {
        super(version);
    }


    public boolean supportsIdentityColumns() {
        return false; // Progress tradicionalmente no soporta IDENTITY como PostgreSQL
    }


    public boolean supportsSequences() {
        return true; // Progress soporta secuencias
    }


    public String getSequenceNextValString(String sequenceName) {
        return "select nextval('" + sequenceName + "')";
    }

    @Override
    public String getCurrentTimestampSelectString() {
        return "select now from sysprogress.syscalctable";
    }


    public boolean supportsLimit() {
        return true;
    }


    public boolean supportsLimitOffset() {
        return false; // Progress no soporta OFFSET de la misma manera que PostgreSQL
    }

    @Override
    public boolean supportsTemporaryTables() {
        return true;
    }


    public String getCreateTemporaryTableString() {
        return "create temp table";
    }


    public boolean dropTemporaryTableAfterUse() {
        return true;
    }

    @Override
    public boolean supportsCurrentTimestampSelection() {
        return true;
    }

    @Override
    public boolean isCurrentTimestampSelectStringCallable() {
        return false;
    }

    @Override
    public boolean supportsUnionAll() {
        return true;
    }

    @Override
    public boolean supportsCommentOn() {
        return false; // Progress generalmente no soporta COMMENT ON
    }

    @Override
    public boolean supportsCascadeDelete() {
        return false; // Limitado en Progress
    }

    @Override
    public boolean supportsOuterJoinForUpdate() {
        return false; // Progress tiene limitaciones con outer joins en updates
    }
}
