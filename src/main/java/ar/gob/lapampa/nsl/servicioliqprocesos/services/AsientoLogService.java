package ar.gob.lapampa.nsl.servicioliqprocesos.services;

import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoLogFilterRequestDTO;

@Service
public interface AsientoLogService {

  GenericResponseDTO listaLogs(AsientoExportRequestDTO asientoRequest);

  GenericResponseDTO byId(Long id);

  GenericResponseDTO listaLogsFiltrado(AsientoLogFilterRequestDTO filterRequest);

}
