package ar.gob.lapampa.nsl.servicioliqprocesos.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Suemovgasban;

@Repository
public interface SuemovgasbanRepository extends JpaRepository<Suemovgasban, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_MOVGASBAN_GENERAR")
  String obtenerMovgasban(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_MOVGASBAN_EXPORTAR")
  String exportarMovgasban(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_MOVGASBAN_ELIMINAR")
  String borrarMovgasban(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  List<Suemovgasban> findByAsientoExportId(Long asientoExportId);

}

