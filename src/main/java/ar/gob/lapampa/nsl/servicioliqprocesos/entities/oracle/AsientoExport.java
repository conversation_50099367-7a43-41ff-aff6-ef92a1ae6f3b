package ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "ASIENTOS_EXPORTS", schema = "LAPAMPA")
@SequenceGenerator(name = "ASIENTOS_EXPORTS_SEQ", sequenceName = "LAPAMPA.ASIENTOS_EXPORTS_ID_SEQ",
    allocationSize = 1)
public class AsientoExport implements Serializable {

  private static final long serialVersionUID = -2966941591902189302L;

  @Id
  @Basic(optional = false)
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ASIENTOS_EXPORTS_SEQ")
  private Long id;

  @Column(name = "CAMPOS")
  @Nonnull
  private String campos;

  @Column(name = "CREADO")
  private Date fechaCreado;

  @Column(name = "PROCESO_ID")
  private Long procesoId;

  @Column(name = "PROCESO_REGISTROS")
  private Long procesoRegistros;

  @Column(name = "PROCESO_INICIO")
  private Date procesoInicio;

  @Column(name = "PROCESO_FINAL")
  private Date procesoFinal;

  @Column(name = "RESULTADO")
  private String resultado;

  @Column(name = "PROCESO_ERRORES")
  private Long procesoErrores;

  @Column(name = "PROCEDIMIENTO")
  private String procedure;

  @Column(name = "NOMBRE")
  private String nombre;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "CABECERAS")
  @Nonnull
  private String cabeceras;

  @Column(name = "URL")
  private String url;

  @Column(name = "REQUEST")
  private String request;

  @Column(name = "USER_LOG")
  private String userLog;

  @Column(name = "EXPORTADO")
  private Date exportado;

  @Column(name = "ANIO")
  private Integer anio;

  @Column(name = "MES")
  private Integer mes;

  @Column(name = "TIPO_PROCESO")
  private String tipoProceso;

  @Column(name = "EXPORTABLE")
  private String exportable;

  @Column(name = "TABLA")
  private String tabla;

  @Column(name = "ASOCIADO")
  private String asociado;

  @Column(name = "PARENT")
  private Long parent;

  @OneToOne
  @JoinColumn(name = "EMPRESA_ID", referencedColumnName = "ID")
  private RrhhEmpresa empresa;
  
  @Column(name = "INS_ID")
  private Long insId;
}
