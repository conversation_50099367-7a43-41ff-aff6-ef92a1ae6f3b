package ar.gob.lapampa.nsl.servicioliqprocesos.mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoLogDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.AsientoLog;


public class AsientoLogMapper {

  private AsientoLogMapper() {}

  public static AsientoLogDTO

      toAsientoLogDTO(AsientoLog asientoLog) {

    if (asientoLog == null) {
      return null;
    }

    AsientoLogDTO asientoLogDTO = new AsientoLogDTO();

    asientoLogDTO.setId(asientoLog.getId());
    asientoLogDTO.setAsientoId(asientoLog.getAsientoId());
    asientoLogDTO.setFechaCreado(asientoLog.getFechaCreado());
    asientoLogDTO.setNombre(asientoLog.getNombre());
    asientoLogDTO.setResultado(asientoLog.getResultado());
    asientoLogDTO.setEstado(asientoLog.getEstado());
    asientoLogDTO.setComentario(asientoLog.getComentario());

    return asientoLogDTO;
  }

  public static List<AsientoLogDTO> toAsientoLogDTOList(List<AsientoLog> list) {
    if (list == null) {
      return Collections.emptyList();
    }

    List<AsientoLogDTO> list1 = new ArrayList<>(list.size());
    for (AsientoLog asientoExport : list) {
      list1.add(toAsientoLogDTO(asientoExport));
    }
    return list1;
  }

  public static AsientoLog

      toAsientoLog(AsientoLogDTO asientoLogDTO) {

    if (asientoLogDTO == null) {
      return null;
    }

    AsientoLog asientoLog = new AsientoLog();

    asientoLog.setId(asientoLogDTO.getId());
    asientoLog.setAsientoId(asientoLogDTO.getAsientoId());
    asientoLog.setFechaCreado(asientoLogDTO.getFechaCreado());
    asientoLog.setNombre(asientoLogDTO.getNombre());
    asientoLog.setResultado(asientoLogDTO.getResultado());
    asientoLog.setEstado(asientoLogDTO.getEstado());
    asientoLog.setComentario(asientoLogDTO.getComentario());

    return asientoLog;
  }

  public static List<AsientoLog> toAsientoLogList(List<AsientoLogDTO> list) {
    if (list == null) {
      return Collections.emptyList();
    }

    List<AsientoLog> list1 = new ArrayList<>(list.size());
    for (AsientoLogDTO asientoLogDTO : list) {
      list1.add(toAsientoLog(asientoLogDTO));
    }
    return list1;
  }
}
