package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.oracle;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarOrdPagRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.SueOrdPagDService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/ordpagd")
public class OrdPagDController {

  @Autowired
  private SueOrdPagDService sueOrdPagDService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','OPAGOD_R','','IS')")
  public GenericResponseDTO listarOrdPagD(@RequestBody ProcesoResquestDTO procesoRequest) {
    return sueOrdPagDService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','OPAGOD_G','','IS')")
  public GenericResponseDTO generarOrdPagD(@RequestBody GenerarOrdPagRequestDTO request) {
    return sueOrdPagDService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('',' OPAGOD_R,OPAGOD_D','','IS')")
  public GenericResponseDTO borrarOrdPagD(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueOrdPagDService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','OPAGOD_EX','','IS')")
  public GenericResponseDTO exportarOrdPagD(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return sueOrdPagDService.exportar(procesoRequest);
  }

}
