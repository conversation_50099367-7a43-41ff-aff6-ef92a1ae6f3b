package ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.impl;

import java.util.List;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarSuetfconceptRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Suetfconcept;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle.SuetfconceptRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.SuetfconceptService;

@Service
public class SuetfconceptServiceImpl implements SuetfconceptService {

  private SuetfconceptRepository suetfconceptRepository;

  public SuetfconceptServiceImpl(SuetfconceptRepository suetfconceptRepository) {
    this.suetfconceptRepository = suetfconceptRepository;
  }

  @Override
  public GenericResponseDTO listar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<Suetfconcept> result =
        suetfconceptRepository.findByAsientoExportId(request.getAsientoExportId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

  @Override
  public GenericResponseDTO generar(GenerarSuetfconceptRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();

    Long asientoExportId = request.getAsientoExportId();
    Long procesoId = request.getProcesoId();
    String resultado = suetfconceptRepository.obtenerTfconcept(asientoExportId, procesoId);

    // Haz lo que necesites con el resultado
    System.out.println("Resultado: " + resultado);

    if (resultado == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Obtenido");
    } else {
      response.setEstadoExito(resultado);
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    // String result =
    // sueliquisusRepository.borrarLiquisus(request.getAsientoExportId(), request.getProcesoId());
    // if (result == null) {
    // response.setEstadoExito(List.of());
    // response.setMensaje("No Borrado");
    // } else {
    // response.setEstadoExito(result);
    // }
    return response;
  }

  @Override
  public GenericResponseDTO exportar(@Valid ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result = suetfconceptRepository.exportarTfconcept(request.getAsientoExportId(),
        request.getProcesoId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Exportado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }



}
