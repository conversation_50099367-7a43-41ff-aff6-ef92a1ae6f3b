package ar.gob.lapampa.nsl.servicioliqprocesos.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/*
 * COMPONENTE BASE DEBE MATENERSE IGUAL EN TODOS LOS MICROSERVICIOS
 */

@ControllerAdvice
public class GlobalExceptionHandler {

  // DTO para una respuesta de error estandarizada
  public record ErrorResponse(String error, String message) {
  }

  @ExceptionHandler(AccessDeniedException.class)
  public ResponseEntity<ErrorResponse> handleAccessDeniedException(AccessDeniedException ex) {
    ErrorResponse errorResponse = new ErrorResponse("Acceso Denegado.", ex.getMessage());
    return new ResponseEntity<>(errorResponse, HttpStatus.FORBIDDEN); // Código 403
  }
}
