package ar.gob.lapampa.nsl.servicioliqprocesos.entities;

import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "TEMP_SUEORDPAGC", schema = "LAPAMPA")
@SequenceGenerator(name = "TEMP_SUEORDPAGC_SEQ", sequenceName = "LAPAMPA.TEMP_SUEORDPAGC_SEQ",
    allocationSize = 1)
public class Sueordpagc {

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TEMP_SUEORDPAGC_SEQ")
  private Long id;

  @Column(name = "ASIENTOEXPORTID")
  private Long asientoExportId;

  @Column(name = "ESTADONSL")
  private String status;

  @Column(name = "CODBAN")
  private Long codBan;

  @Column(name = "CODHAB")
  private String codHab;

  @Column(name = "CODIMP")
  private String codImp;

  @Column(name = "CPAG")
  private Long cPag;

  @Column(name = "CTALECOP")
  private Long ctaLecop;

  @Column(name = "CTAPESOS")
  private Long ctaPesos;

  @Column(name = "CTASIS")
  private Long ctaSis;

  @Column(name = "CTASISL")
  private Long ctaSisL;

  @Column(name = "CTASUC")
  private Long hora;

  @Column(name = "CTASUCL")
  private Long ctaSucL;

  @Column(name = "CTATIPO")
  private Long ctaTipo;

  @Column(name = "CTATIPOL")
  private Long ctaTipoL;

  @Column(name = "CUENTA")
  private Long cuenta;

  @Column(name = "ESTPED")
  private String estPed;

  @Column(name = "FECHAENV")
  private Date fechaEnv;

  @Column(name = "FECHAPED")
  private Date fechaPed;

  @Column(name = "LIQANIO")
  private Long liqanio;

  @Column(name = "LIQMES")
  private Long liqmes;

  @Column(name = "LIQTIPO")
  private String liqtipo;

  @Column(name = "MARCA")
  private String marca;

  @Column(name = "NROPED")
  private Long nroPed;

  @Column(name = "TOTBRUTO")
  private BigDecimal totBruto;

  @Column(name = "TOTCRED")
  private BigDecimal totCred;

  @Column(name = "TOTCREDL")
  private BigDecimal totCredL;

  @Column(name = "TOTNETO")
  private BigDecimal totNeto;

  @Column(name = "TOTNETOL")
  private BigDecimal totNetoL;

  @Column(name = "MARCACOPIADO")
  private String marcaCopiado;

}
