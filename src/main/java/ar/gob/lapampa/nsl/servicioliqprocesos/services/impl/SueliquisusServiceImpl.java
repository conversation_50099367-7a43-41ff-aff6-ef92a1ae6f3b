package ar.gob.lapampa.nsl.servicioliqprocesos.services.impl;

import java.util.List;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Sueliquisus;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.SueliquisusRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.SueliquisusService;

@Service
public class SueliquisusServiceImpl implements SueliquisusService {

  private SueliquisusRepository sueliquisusRepository;

  public SueliquisusServiceImpl(SueliquisusRepository sueliquisusRepository) {
    this.sueliquisusRepository = sueliquisusRepository;
  }

  @Override
  public GenericResponseDTO listar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<Sueliquisus> result =
        sueliquisusRepository.findByAsientoExportId(request.getAsientoExportId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

  @Override
  public GenericResponseDTO generar(GenerarLiquisusRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    Long asientoExportId = request.getAsientoExportId();
    Long procesoId = request.getProcesoId();
    String result = sueliquisusRepository.obtenerLiquisus(asientoExportId, procesoId);
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Obtenido");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result =
        sueliquisusRepository.borrarLiquisus(request.getAsientoExportId(), request.getProcesoId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Borrado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

  @Override
  public GenericResponseDTO exportar(@Valid ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result = sueliquisusRepository.exportarLiquisus(request.getAsientoExportId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Exportado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }
}
