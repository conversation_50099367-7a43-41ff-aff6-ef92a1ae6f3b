package ar.gob.lapampa.nsl.servicioliqprocesos.controllers.oracle;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle.TfDetalleService;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/tfdetalle")
public class TfDetalleController {

  @Autowired
  private TfDetalleService tfDetalleService;

  @PostMapping("/listar")
  @PreAuthorize("@cs.hasAccess('','TFDET_R','','IS')")
  public GenericResponseDTO listarTfDetalle(@RequestBody ProcesoResquestDTO procesoRequest) {
    return tfDetalleService.listar(procesoRequest);
  }

  @PostMapping("/generar")
  @PreAuthorize("@cs.hasAccess('','TFDET_G','','IS')")
  public GenericResponseDTO generarTfDetalle(@RequestBody GenerarLiquisusRequestDTO request) {
    return tfDetalleService.generar(request);
  }

  @PostMapping("/borrar")
  @PreAuthorize("@cs.hasAccess('','TFDET_R, TFDET_D','','IS')")
  public GenericResponseDTO borrarTfDetalle(@RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return tfDetalleService.borrar(procesoRequest);
  }

  @PostMapping("/exportar")
  @PreAuthorize("@cs.hasAccess('','TFDET_EX','','IS')")
  public GenericResponseDTO exportarTfDetalle(
      @RequestBody @Valid ProcesoResquestDTO procesoRequest) {
    return tfDetalleService.exportar(procesoRequest);
  }

}
