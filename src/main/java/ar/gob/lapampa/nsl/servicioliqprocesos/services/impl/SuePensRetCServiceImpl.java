package ar.gob.lapampa.nsl.servicioliqprocesos.services.impl;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarPensRetRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.AsientoExport;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.LiqDepositoBlpAnses;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Suepensretc;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.AsientoExportRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.DepoBlpAnsesRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.SuepensretcRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.SuePensRetCService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SuePensRetCServiceImpl implements SuePensRetCService {

  private SuepensretcRepository suepensretcRepository;
  private DepoBlpAnsesRepository depoBlpAnsesRepository;
  private AsientoExportRepository asientoExportRepository;

  public SuePensRetCServiceImpl(SuepensretcRepository suepensretcRepository,
      DepoBlpAnsesRepository depoBlpAnsesRepository,
      AsientoExportRepository asientoExportRepository) {
    this.suepensretcRepository = suepensretcRepository;
    this.depoBlpAnsesRepository = depoBlpAnsesRepository;
    this.asientoExportRepository = asientoExportRepository;
  }

  @Override
  public GenericResponseDTO listar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<Suepensretc> result =
        suepensretcRepository.findByAsientoExportId(request.getAsientoExportId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

  @Override
  public GenericResponseDTO generar(GenerarPensRetRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    Long asientoExportId = request.getAsientoExportId();
    Long procesoId = request.getProcesoId();
    List<LiqDepositoBlpAnses> bplAnses = depoBlpAnsesRepository.findByProcesoId(procesoId);
    if (bplAnses.isEmpty()) {
      Optional<AsientoExport> optAsiento = asientoExportRepository.findById(asientoExportId);
      if (optAsiento.isPresent()) {
        AsientoExport asiento = optAsiento.get();
        asiento.setEstado("Encolado (Falta BLP !)");
        asientoExportRepository.save(asiento);
      }
      response.setEstadoError("Falta generar BLP Anses para el proceso Id: " + procesoId);
    } else {
      // String fechaHasta = request.getFechaHasta();
      SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");
      formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
      String result;
      // try {
      // java.util.Date fecha = formatter.parse(fechaHasta);
      // formatter = new SimpleDateFormat("yyyy-MM-dd");
      // Date fechaSQL = Date.valueOf(formatter.format(fecha));
      // log.info("Fecha Hasta: {}", fecha);
      // result = suepensretcRepository.obtenerPensRetC(asientoExportId, procesoId, fechaSQL);
      result = suepensretcRepository.obtenerPensRetC(asientoExportId, procesoId);
      if (result == null) {
        response.setEstadoExito(List.of());
        response.setMensaje("No Obtenido");
      } else {
        response.setEstadoExito(result);
      }
      // } catch (ParseException e) {
      // e.printStackTrace();
      // }
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result =
        suepensretcRepository.borrarPensRetC(request.getAsientoExportId(), request.getProcesoId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Borrado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

  @Override
  public GenericResponseDTO exportar(@Valid ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result = suepensretcRepository.exportarPensRetC(request.getAsientoExportId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Exportado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }
}
