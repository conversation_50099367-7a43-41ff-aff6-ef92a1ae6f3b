package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Suepjcoretc;

@Repository
public interface SuepjcoretcRepository extends JpaRepository<Suepjcoretc, Long> {

  List<Suepjcoretc> findByAsientoExportId(Long id);

}
