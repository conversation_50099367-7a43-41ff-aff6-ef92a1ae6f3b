package ar.gob.lapampa.nsl.servicioliqprocesos.services.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoLogDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoLogFilterRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.AsientoLog;
import ar.gob.lapampa.nsl.servicioliqprocesos.mappers.AsientoLogMapper;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.AsientoLogRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.AsientoLogService;

@Service
public class AsientoLogServiceImpl implements AsientoLogService {

  private AsientoLogRepository AsientoLogRepository;

  public AsientoLogServiceImpl(AsientoLogRepository AsientoLogRepository) {
    super();
    this.AsientoLogRepository = AsientoLogRepository;
  }

  private Long asientoId = 0L;

  @Override
  public GenericResponseDTO listaLogs(AsientoExportRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String currentPrincipalName = "";
    List<AsientoLogDTO> resp;
    if (authentication != null) {
      currentPrincipalName = authentication.getName();
      resp = AsientoLogMapper.toAsientoLogDTOList(AsientoLogRepository
          .findByAsientoIdAndNombre(request.getProcesoId(), currentPrincipalName));
    } else {
      response.setEstadoError("User Login not found");
      response.setMensaje("User Login ausente");
      return response;
    }

    if (request.getProcesoId() == null) {
      resp = AsientoLogMapper
          .toAsientoLogDTOList(AsientoLogRepository.findByNombre(currentPrincipalName));
    } else {
      if (request.getEstado() == null) {
        resp = AsientoLogMapper.toAsientoLogDTOList(AsientoLogRepository
            .findByAsientoIdAndNombre(request.getProcesoId(), currentPrincipalName));
      } else {
        resp = AsientoLogMapper.toAsientoLogDTOList(
            AsientoLogRepository.findByAsientoIdAndEstadoAndNombre(request.getProcesoId(),
                request.getEstado(), currentPrincipalName));
      }
    }
    response.setEstadoExito(resp);
    response.setMensaje("Registros: " + resp.size());
    return response;
  }



  @Override
  public GenericResponseDTO byId(Long id) {
    GenericResponseDTO response = new GenericResponseDTO();
    Optional<AsientoLog> optionalAsiento = AsientoLogRepository.findById(id);
    if (optionalAsiento.isPresent()) {
      AsientoLogDTO asientoDto = AsientoLogMapper.toAsientoLogDTO(optionalAsiento.get());
      response.setEstadoExito(asientoDto);
    } else {
      response.setEstadoError("No se encontraron asientos para el id: " + id);
    }
    return response;
  }

  @Override
  public GenericResponseDTO listaLogsFiltrado(AsientoLogFilterRequestDTO filterRequest) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<Long> listaProcesos = new ArrayList<>();


    return response;
  }


}
