package ar.gob.lapampa.nsl.servicioliqprocesos.repositories.oracle;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.oracle.Sueliquisus;

@Repository
public interface SueliquisusRepository extends JpaRepository<Sueliquisus, Long> {

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_LIQUISUS_GENERAR")
  String obtenerLiquisus(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_LIQUISUS_EXPORTAR")
  String exportarLiquisus(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId);

  @Transactional
  @Modifying
  @Procedure(value = "LAPAMPA.ASIENTOS_LIQUISUS_ELIMINAR")
  String borrarLiquisus(@Param("P_ASIENTOSEXPORT_ID") Long asientoExportId,
      @Param("P_PROCESO_ID") Long procesoId);

  List<Sueliquisus> findByAsientoExportId(Long asientoExportId);

}
