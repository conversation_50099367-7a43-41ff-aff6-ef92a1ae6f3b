package ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle;

import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarOrdPagRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;

@Service
public interface SueOrdPagDService {

  GenericResponseDTO listar(ProcesoResquestDTO request);

  GenericResponseDTO borrar(ProcesoResquestDTO request);

  GenericResponseDTO exportar(@Valid ProcesoResquestDTO request);

  GenericResponseDTO generar(GenerarOrdPagRequestDTO request);

}
