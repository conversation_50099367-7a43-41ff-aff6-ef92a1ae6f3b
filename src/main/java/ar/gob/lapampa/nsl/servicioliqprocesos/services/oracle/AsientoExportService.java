package ar.gob.lapampa.nsl.servicioliqprocesos.services.oracle;

import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.JsonNode;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoDescriptorDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportFilterRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportGenerarRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.AsientoExportRequestDTO;

@Service
public interface AsientoExportService {

  GenericResponseDTO listaProcesos(AsientoExportRequestDTO asientoRequest);

  GenericResponseDTO generarProcesos(AsientoExportGenerarRequestDTO asientoRequest);

  GenericResponseDTO byId(Long id);

  GenericResponseDTO listaProcesosFiltrado(AsientoExportFilterRequestDTO filterRequest);

  GenericResponseDTO listarNombres();

  AsientoDescriptorDTO obtieneAsientoDeJson(JsonNode liquisus);

  GenericResponseDTO listaEmpresasPorProcesoId(Long procesoId);

}
