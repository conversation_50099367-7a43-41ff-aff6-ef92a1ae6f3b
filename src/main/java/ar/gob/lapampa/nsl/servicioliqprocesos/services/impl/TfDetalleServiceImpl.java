package ar.gob.lapampa.nsl.servicioliqprocesos.services.impl;

import java.util.List;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.GenerarLiquisusRequestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.dtos.ProcesoResquestDTO;
import ar.gob.lapampa.nsl.servicioliqprocesos.entities.Suetfdetalle;
import ar.gob.lapampa.nsl.servicioliqprocesos.repositories.SuetfdetalleRepository;
import ar.gob.lapampa.nsl.servicioliqprocesos.services.TfDetalleService;

@Service
public class TfDetalleServiceImpl implements TfDetalleService {

  private SuetfdetalleRepository suetfdetalleRepository;


  public TfDetalleServiceImpl(SuetfdetalleRepository suetfdetalleRepository) {
    this.suetfdetalleRepository = suetfdetalleRepository;
  }

  @Override
  public GenericResponseDTO listar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    List<Suetfdetalle> result =
        suetfdetalleRepository.findByAsientoExportId(request.getAsientoExportId());
    if (result.isEmpty()) {
      response.setEstadoExito(List.of());
      response.setMensaje("No encontrado");
    } else {
      response.setEstadoExito(result);
      response.setMensaje(Integer.toString(result.size()));
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrar(ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result = suetfdetalleRepository.borrarSuetfdetalle(request.getAsientoExportId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Borrado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

  @Override
  public GenericResponseDTO exportar(@Valid ProcesoResquestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    String result = suetfdetalleRepository.exportarSuetfdetalle(request.getAsientoExportId());
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Exportado");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

  @Override
  public GenericResponseDTO generar(GenerarLiquisusRequestDTO request) {
    GenericResponseDTO response = new GenericResponseDTO();
    Long asientoExportId = request.getAsientoExportId();
    Long procesoId = request.getProcesoId();
    String result = suetfdetalleRepository.obtenerSuetfdetalle(asientoExportId, procesoId);
    if (result == null) {
      response.setEstadoExito(List.of());
      response.setMensaje("No Obtenido");
    } else {
      response.setEstadoExito(result);
    }
    return response;
  }

}
